Manifest-Version: 1.0
Created-By: <PERSON><PERSON> 3.2.0
Build-Jdk-Spec: 17
Class-Path: lib/spring-boot-devtools-2.5.14.jar lib/spring-boot-2.5.14.j
 ar lib/spring-core-5.3.20.jar lib/spring-jcl-5.3.20.jar lib/spring-cont
 ext-5.3.20.jar lib/spring-beans-5.3.20.jar lib/spring-expression-5.3.20
 .jar lib/spring-boot-autoconfigure-2.5.14.jar lib/druid-spring-boot-sta
 rter-1.2.16.jar lib/druid-1.2.16.jar lib/slf4j-api-1.7.36.jar lib/mysql
 -connector-java-8.0.29.jar lib/template-framework-service-1.0.0.jar lib
 /spring-boot-starter-web-2.5.14.jar lib/spring-boot-starter-json-2.5.14
 .jar lib/jackson-databind-2.12.6.1.jar lib/jackson-annotations-2.12.6.j
 ar lib/jackson-core-2.12.6.jar lib/jackson-datatype-jdk8-2.12.6.jar lib
 /jackson-datatype-jsr310-2.12.6.jar lib/jackson-module-parameter-names-
 2.12.6.jar lib/spring-boot-starter-tomcat-2.5.14.jar lib/tomcat-embed-c
 ore-9.0.63.jar lib/tomcat-embed-el-9.0.63.jar lib/tomcat-embed-websocke
 t-9.0.63.jar lib/spring-web-5.3.20.jar lib/spring-webmvc-5.3.20.jar lib
 /spring-boot-starter-aop-2.5.14.jar lib/spring-aop-5.3.20.jar lib/aspec
 tjweaver-1.9.7.jar lib/mybatis-plus-boot-starter-3.5.3.jar lib/mybatis-
 plus-3.5.3.jar lib/mybatis-plus-extension-3.5.3.jar lib/mybatis-plus-co
 re-3.5.3.jar lib/mybatis-plus-annotation-3.5.3.jar lib/jsqlparser-4.4.j
 ar lib/mybatis-3.5.10.jar lib/mybatis-spring-2.0.7.jar lib/spring-boot-
 starter-jdbc-2.5.14.jar lib/HikariCP-4.0.3.jar lib/spring-jdbc-5.3.20.j
 ar lib/spring-tx-5.3.20.jar lib/kaptcha-2.3.3.jar lib/filters-2.0.235-1
 .jar lib/servlet-api-2.5.jar lib/oshi-core-6.4.0.jar lib/jna-5.12.1.jar
  lib/jna-platform-5.12.1.jar lib/template-server-service-1.0.0.jar lib/
 template-common-service-1.0.0.jar lib/spring-context-support-5.3.20.jar
  lib/spring-test-5.3.20.jar lib/spring-boot-starter-security-2.5.14.jar
  lib/spring-security-config-5.5.8.jar lib/spring-security-core-5.5.8.ja
 r lib/spring-security-crypto-5.5.8.jar lib/spring-security-web-5.5.8.ja
 r lib/pagehelper-spring-boot-starter-1.4.6.jar lib/mybatis-spring-boot-
 starter-2.2.2.jar lib/mybatis-spring-boot-autoconfigure-2.2.2.jar lib/p
 agehelper-spring-boot-autoconfigure-1.4.6.jar lib/pagehelper-5.3.2.jar 
 lib/spring-boot-starter-validation-2.5.14.jar lib/hibernate-validator-6
 .2.3.Final.jar lib/jakarta.validation-api-2.0.2.jar lib/jboss-logging-3
 .4.3.Final.jar lib/classmate-1.5.1.jar lib/commons-lang3-3.12.0.jar lib
 /dynamic-datasource-spring-boot-starter-3.5.2.jar lib/fastjson2-2.0.25.
 jar lib/commons-io-2.11.0.jar lib/poi-ooxml-4.1.2.jar lib/poi-4.1.2.jar
  lib/commons-collections4-4.4.jar lib/commons-math3-3.6.1.jar lib/Spars
 eBitSet-1.2.jar lib/poi-ooxml-schemas-4.1.2.jar lib/xmlbeans-3.1.0.jar 
 lib/commons-compress-1.19.jar lib/curvesapi-1.06.jar lib/jjwt-0.9.1.jar
  lib/jaxb-api-2.3.1.jar lib/javax.activation-api-1.2.0.jar lib/spring-b
 oot-starter-data-redis-2.5.14.jar lib/spring-data-redis-2.5.11.jar lib/
 spring-data-keyvalue-2.5.11.jar lib/spring-data-commons-2.5.11.jar lib/
 spring-oxm-5.3.20.jar lib/lettuce-core-6.1.8.RELEASE.jar lib/netty-comm
 on-4.1.77.Final.jar lib/netty-handler-4.1.77.Final.jar lib/netty-resolv
 er-4.1.77.Final.jar lib/netty-buffer-4.1.77.Final.jar lib/netty-codec-4
 .1.77.Final.jar lib/netty-transport-4.1.77.Final.jar lib/reactor-core-3
 .4.18.jar lib/commons-pool2-2.9.0.jar lib/UserAgentUtils-1.21.jar lib/j
 avax.servlet-api-4.0.1.jar lib/lombok-1.18.24.jar lib/spring-boot-start
 er-websocket-2.5.14.jar lib/spring-messaging-5.3.20.jar lib/spring-webs
 ocket-5.3.20.jar lib/gson-2.9.1.jar lib/bcpkix-jdk15on-1.57.jar lib/bcp
 rov-jdk15on-1.57.jar lib/commons-lang-2.6.jar lib/commons-logging-1.2.j
 ar lib/httpclient-4.5.jar lib/commons-codec-1.15.jar lib/httpcore-4.4.1
 .jar lib/wechatpay-java-0.2.11.jar lib/wechatpay-java-core-0.2.11.jar l
 ib/core-3.3.3.jar lib/hutool-all-5.8.12.jar lib/documentautoml20221229-
 1.0.11.jar lib/tea-util-0.2.16.jar lib/tea-openapi-0.2.8.jar lib/creden
 tials-java-0.2.4.jar lib/jaxb-core-2.3.0.jar lib/jaxb-impl-2.3.0.jar li
 b/alibabacloud-gateway-spi-0.0.1.jar lib/tea-xml-0.1.5.jar lib/dom4j-2.
 0.3.jar lib/org.jacoco.agent-0.8.4-runtime.jar lib/openapiutil-0.2.1.ja
 r lib/endpoint-util-0.0.7.jar lib/tea-1.2.0.jar lib/tencentcloud-sdk-ja
 va-common-3.1.1317.jar lib/okhttp-3.14.9.jar lib/okio-1.17.2.jar lib/lo
 gging-interceptor-3.14.9.jar lib/ini4j-0.5.4.jar lib/tencentcloud-sdk-j
 ava-ocr-3.1.1047.jar lib/oapi-java-sdk-release-V4-2.0.2.jar lib/okhttp-
 sse-3.14.9.jar lib/java-jwt-4.2.2.jar lib/annotations-2.0.1.jar lib/fas
 tjson-1.2.73.jar lib/rxjava-3.1.8.jar lib/reactive-streams-1.0.3.jar li
 b/adapter-rxjava2-2.9.0.jar lib/retrofit-2.9.0.jar lib/rxjava-2.2.21.ja
 r lib/converter-gson-2.9.0.jar lib/converter-jackson-2.9.0.jar lib/mbkn
 or-jackson-jsonschema_2.13-1.0.39.jar lib/scala-library-2.13.1.jar lib/
 kotlin-scripting-compiler-embeddable-1.3.50.jar lib/kotlin-scripting-co
 mpiler-impl-embeddable-1.3.50.jar lib/kotlin-scripting-common-1.5.32.ja
 r lib/kotlinx-coroutines-core-jvm-1.5.2.jar lib/kotlin-stdlib-jdk8-1.5.
 32.jar lib/kotlin-stdlib-jdk7-1.5.32.jar lib/kotlin-scripting-jvm-1.5.3
 2.jar lib/kotlin-script-runtime-1.5.32.jar lib/kotlinx-coroutines-core-
 1.5.2.jar lib/kotlin-stdlib-1.5.32.jar lib/annotations-13.0.jar lib/kot
 lin-stdlib-common-1.5.32.jar lib/validation-api-2.0.1.Final.jar lib/cla
 ssgraph-4.8.21.jar lib/spring-boot-starter-thymeleaf-2.5.14.jar lib/spr
 ing-boot-starter-2.5.14.jar lib/spring-boot-starter-logging-2.5.14.jar 
 lib/logback-classic-1.2.11.jar lib/logback-core-1.2.11.jar lib/log4j-to
 -slf4j-2.17.2.jar lib/log4j-api-2.17.2.jar lib/jul-to-slf4j-1.7.36.jar 
 lib/jakarta.annotation-api-1.3.5.jar lib/snakeyaml-1.28.jar lib/thymele
 af-spring5-3.0.15.RELEASE.jar lib/thymeleaf-3.0.15.RELEASE.jar lib/atto
 parser-2.0.5.RELEASE.jar lib/unbescape-1.1.6.RELEASE.jar lib/thymeleaf-
 extras-java8time-3.0.4.RELEASE.jar
Main-Class: com.youying.ApplicationMain

