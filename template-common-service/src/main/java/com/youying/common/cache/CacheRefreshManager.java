package com.youying.common.cache;

import com.youying.common.event.DatabaseChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 缓存刷新管理器
 * 负责监听数据库变更事件并调用相应的缓存刷新策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CacheRefreshManager {

    @Autowired(required = false)
    private List<CacheRefreshStrategy> strategies;

    /**
     * 表名到策略的映射
     */
    private final Map<String, List<CacheRefreshStrategy>> tableStrategyMap = new ConcurrentHashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        if (strategies == null || strategies.isEmpty()) {
            log.info("No cache refresh strategies found");
            return;
        }

        // 按表名分组并按优先级排序
        tableStrategyMap.putAll(
                strategies.stream()
                        .collect(Collectors.groupingBy(
                                CacheRefreshStrategy::getSupportedTableName,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        list -> list.stream()
                                                .sorted(Comparator.comparingInt(CacheRefreshStrategy::getPriority))
                                                .collect(Collectors.toList())
                                )
                        ))
        );

        log.info("Initialized cache refresh strategies for tables: {}", tableStrategyMap.keySet());
    }

    /**
     * 监听数据库变更事件
     *
     * @param event 数据库变更事件
     */
    @EventListener
    public void handleDatabaseChangeEvent(DatabaseChangeEvent event) {
        String tableName = event.getTableName();
        List<CacheRefreshStrategy> tableStrategies = tableStrategyMap.get(tableName);

        if (tableStrategies == null || tableStrategies.isEmpty()) {
            log.debug("No cache refresh strategy found for table: {}", tableName);
            return;
        }

        log.info("Processing database change event for table: {}, operation: {}, primaryKey: {}",
                tableName, event.getOperationType(), event.getPrimaryKey());

        // 执行所有相关策略
        for (CacheRefreshStrategy strategy : tableStrategies) {
            try {
                if (strategy.isAsyncSupported()) {
                    refreshCacheAsync(strategy, event);
                } else {
                    strategy.refreshCache(event);
                }
            } catch (Exception e) {
                log.error("Error executing cache refresh strategy {} for table {}: {}",
                        strategy.getClass().getSimpleName(), tableName, e.getMessage(), e);
            }
        }
    }

    /**
     * 异步刷新缓存
     *
     * @param strategy 缓存刷新策略
     * @param event    数据库变更事件
     */
    @Async
    public void refreshCacheAsync(CacheRefreshStrategy strategy, DatabaseChangeEvent event) {
        try {
            strategy.refreshCache(event);
            log.debug("Successfully executed async cache refresh strategy {} for table {}",
                    strategy.getClass().getSimpleName(), event.getTableName());
        } catch (Exception e) {
            log.error("Error in async cache refresh strategy {} for table {}: {}",
                    strategy.getClass().getSimpleName(), event.getTableName(), e.getMessage(), e);
        }
    }

    /**
     * 获取支持的表名列表
     *
     * @return 支持的表名列表
     */
    public List<String> getSupportedTables() {
        return tableStrategyMap.keySet().stream().sorted().collect(Collectors.toList());
    }

    /**
     * 手动触发缓存刷新
     *
     * @param tableName     表名
     * @param operationType 操作类型
     * @param primaryKey    主键
     */
    public void manualRefresh(String tableName, DatabaseChangeEvent.OperationType operationType, Object primaryKey) {
        DatabaseChangeEvent event = new DatabaseChangeEvent(this, tableName, operationType, primaryKey, null, null);
        handleDatabaseChangeEvent(event);
    }
}
