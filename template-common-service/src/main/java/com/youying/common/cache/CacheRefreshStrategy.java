package com.youying.common.cache;

import com.youying.common.event.DatabaseChangeEvent;

/**
 * 缓存刷新策略接口
 * 定义了当数据库发生变更时如何刷新相关缓存的策略
 *
 * <AUTHOR>
 */
public interface CacheRefreshStrategy {

    /**
     * 获取支持的表名
     *
     * @return 表名
     */
    String getSupportedTableName();

    /**
     * 刷新缓存
     *
     * @param event 数据库变更事件
     */
    void refreshCache(DatabaseChangeEvent event);

    /**
     * 获取策略优先级，数字越小优先级越高
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 是否支持异步执行
     *
     * @return true表示支持异步执行
     */
    default boolean isAsyncSupported() {
        return true;
    }
}
