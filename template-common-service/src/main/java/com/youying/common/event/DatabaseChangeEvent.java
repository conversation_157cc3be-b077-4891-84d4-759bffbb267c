package com.youying.common.event;

import java.util.Map;

import org.springframework.context.ApplicationEvent;

/**
 * 数据库变更事件
 * 当数据库表发生INSERT、UPDATE、DELETE操作时触发此事件
 *
 * <AUTHOR>
 */
public class DatabaseChangeEvent extends ApplicationEvent {

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        INSERT, UPDATE, DELETE
    }

    /**
     * 表名
     */
    private final String tableName;

    /**
     * 操作类型
     */
    private final OperationType operationType;

    /**
     * 主键ID（如果有的话）
     */
    private final Object primaryKey;

    /**
     * 变更前的数据（UPDATE和DELETE时有值）
     */
    private final Map<String, Object> oldData;

    /**
     * 变更后的数据（INSERT和UPDATE时有值）
     */
    private final Map<String, Object> newData;

    /**
     * 事件发生时间戳
     */
    private final long timestamp;

    /**
     * 构造函数
     *
     * @param source        事件源
     * @param tableName     表名
     * @param operationType 操作类型
     * @param primaryKey    主键ID
     * @param oldData       变更前数据
     * @param newData       变更后数据
     */
    public DatabaseChangeEvent(Object source, String tableName, OperationType operationType,
            Object primaryKey, Map<String, Object> oldData, Map<String, Object> newData) {
        super(source);
        this.tableName = tableName;
        this.operationType = operationType;
        this.primaryKey = primaryKey;
        this.oldData = oldData;
        this.newData = newData;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 创建INSERT事件
     */
    public static DatabaseChangeEvent createInsertEvent(Object source, String tableName, Object primaryKey,
            Map<String, Object> newData) {
        return new DatabaseChangeEvent(source, tableName, OperationType.INSERT, primaryKey, null, newData);
    }

    /**
     * 创建UPDATE事件
     */
    public static DatabaseChangeEvent createUpdateEvent(Object source, String tableName, Object primaryKey,
            Map<String, Object> oldData, Map<String, Object> newData) {
        return new DatabaseChangeEvent(source, tableName, OperationType.UPDATE, primaryKey, oldData, newData);
    }

    /**
     * 创建DELETE事件
     */
    public static DatabaseChangeEvent createDeleteEvent(Object source, String tableName, Object primaryKey,
            Map<String, Object> oldData) {
        return new DatabaseChangeEvent(source, tableName, OperationType.DELETE, primaryKey, oldData, null);
    }

    // Getter methods
    public String getTableName() {
        return tableName;
    }

    public OperationType getOperationType() {
        return operationType;
    }

    public Object getPrimaryKey() {
        return primaryKey;
    }

    public Map<String, Object> getOldData() {
        return oldData;
    }

    public Map<String, Object> getNewData() {
        return newData;
    }

    public long getEventTimestamp() {
        return timestamp;
    }

    @Override
    public String toString() {
        return "DatabaseChangeEvent{" +
                "tableName='" + tableName + '\'' +
                ", operationType=" + operationType +
                ", primaryKey=" + primaryKey +
                ", timestamp=" + timestamp +
                '}';
    }
}
