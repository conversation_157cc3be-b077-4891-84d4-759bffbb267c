package com.youying.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 验证码登录前缀
     */
    public static final String PHONE_CODE_LOGIN_KEY = "login:phone_code:";

    /**
     * 微信登录前缀
     */
    public static final String WECHAT_LOGIN_KEY = "WECHAT_LOGIN_KEY";

    /**
     * 忘记密码验证码前缀
     */
    public static final String UPDATE_PWD_KEY = "update_pwd_key:";

    /**
     * 修改密码手机验证码
     */
    public static final String UPDATE_PWD_PHONE_KEY = "update_pwd_phone_key:";

    /**
     * 修改密码手机验证码5分钟
     */
    public static final Integer UPDATE_PWD_PHONE_TIME = 5;

    /**
     * 发送修改密码手机验证码(旧号码)
     */
    public static final String UPDATE_PHONE_KEY = "update_phone_key:";

    /**
     * 发送修改密码手机验证码(新号码)
     */
    public static final String UPDATE_NEW_PHONE_KEY = "update_new_phone_key:";

    /**
     * 发送修改密码手机验证码(新号码)
     */
    public static final String LOGOUT_PHONE_KEY = "logout_phone_key:";

    /**
     * 修改手机验证码5分钟
     */
    public static final Integer UPDATE_PHONE_TIME = 5;

    /**
     * 百度AI识别前缀
     */
    public static final String BAI_DU_AI = "baidu_ai";

    /**
     * 百度AI识别过期时间前缀
     */
    public static final Integer BAI_DU_TIME = 2590000;

    /**
     * 剧目相关缓存 redis key
     */
    public static final String REPERTOIRE_CACHE_KEY = "repertoire:";

    /**
     * 剧目列表缓存 redis key
     */
    public static final String REPERTOIRE_LIST_CACHE_KEY = "repertoire:list:";

    /**
     * 剧目详情缓存 redis key
     */
    public static final String REPERTOIRE_INFO_CACHE_KEY = "repertoire:info:";

    /**
     * 剧场相关缓存 redis key
     */
    public static final String THEATER_CACHE_KEY = "theater:";

    /**
     * 剧场列表缓存 redis key
     */
    public static final String THEATER_LIST_CACHE_KEY = "theater:list:";

    /**
     * 剧场详情缓存 redis key
     */
    public static final String THEATER_INFO_CACHE_KEY = "theater:info:";
}
