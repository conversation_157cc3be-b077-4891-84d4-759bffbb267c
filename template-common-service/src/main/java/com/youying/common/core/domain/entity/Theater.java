package com.youying.common.core.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 剧场表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_theater")
public class Theater extends Model<Theater> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 剧场名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 短剧场名
     */
    @TableField("short_name")
    private String shortName;

    /**
     * 联系人
     */
    @TableField("contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 省
     */
    @TableField("prov_id")
    private Long provId;

    /**
     * 市
     */
    @TableField("city_id")
    private Long cityId;

    /**
     * 区
     */
    @TableField("area_id")
    private Long areaId;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 封面图URL
     */
    @TableField("cover_picture")
    private String coverPicture;

    /**
     * 剧目图片,拼接
     */
    @TableField("pictures")
    private String pictures;

    /**
     * 好评率
     */
    @TableField("good_rating_rate")
    private Double goodRatingRate;

    /**
     * 关注数量
     */
    @TableField("focus_number")
    private Integer focusNumber;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 推荐（0否，1是）
     */
    @TableField("recommend")
    private Integer recommend;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    @TableField("audit")
    private Integer audit;

    /**
     * 审核通过时间
     */
    @TableField("audit_pass_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 驳回原因
     */
    @TableField("reasons_rejection")
    private String reasonsRejection;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
