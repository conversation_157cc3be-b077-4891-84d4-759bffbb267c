package com.youying.web.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.TimerTask;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.util.StringUtil;
import com.youying.common.annotation.Anonymous;
import com.youying.common.config.FriendBetterConfig;
import com.youying.common.core.common.AITextResponse;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.ImageScanRecord;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.common.core.domain.entity.PortfolioInfo;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.core.domain.entity.RepertoireInfo;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.core.domain.entity.Theater;
import com.youying.common.core.domain.entity.TicketKey;
import com.youying.common.core.domain.entity.UserDigitalAvatar;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.core.redis.RedisCache;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.DateUtils;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.StringUtils;
import com.youying.common.utils.file.FileUploadUtils;
import com.youying.framework.imgagediscern.AliyunAi;
import com.youying.framework.imgagediscern.DeepSeekAi;
import com.youying.framework.imgagediscern.TencentAi;
import com.youying.framework.manager.AsyncManager;
import com.youying.system.domain.portfolio.PortfolioResponse;
import com.youying.system.domain.repertoire.RepertoireCacheInfo;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;
import com.youying.system.service.DigitalAvatarService;
import com.youying.system.service.ImageScanRecordService;
import com.youying.system.service.PortfolioInfoService;
import com.youying.system.service.PortfolioService;
import com.youying.system.service.RepertoireInfoDetailService;
import com.youying.system.service.RepertoireInfoService;
import com.youying.system.service.RepertoireService;
import com.youying.system.service.RepertoireTicketService;
import com.youying.system.service.TheaterService;
import com.youying.system.service.TicketKeyService;
import com.youying.system.service.UserDigitalAvatarService;
import com.youying.system.service.UserReceivingRecordsService;
import com.youying.system.service.impl.UserReceivingRecordsServiceImpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 百度AI扫描识别
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@RestController
@RequestMapping("/ai")
@Slf4j
public class AiIdentificationController {
    private final String ZERO = "0";
    private final BigDecimal MAX_PRICE = new BigDecimal("9999");
    @Autowired
    private RepertoireService repertoireService;
    @Autowired
    private RepertoireInfoDetailService repertoireInfoDetailService;
    @Autowired
    private DigitalAvatarService digitalAvatarService;
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private PortfolioService portfolioService;
    @Autowired
    private PortfolioInfoService portfolioInfoService;
    @Autowired
    private UserDigitalAvatarService userDigitalAvatarService;
    @Autowired
    private RepertoireTicketService repertoireTicketService;
    @Autowired
    private ImageScanRecordService imageScanRecordService;
    @Autowired
    private TicketKeyService ticketKeyService;
    @Autowired
    private TheaterService theaterService;

    @Autowired
    private RepertoireInfoService repertoireInfoService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RepertoireMatchOptimizer repertoireMatchOptimizer;

    @Autowired
    private TencentAi tencentAi;

    /**
     * 纸质票扫描API
     */
    @PostMapping(value = "/imgRecognition", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @Anonymous
    public R<AITextResponse> testImgRecognition(String deviceModel, MultipartFile file) {
        String imgBase64 = "";
        try {
            imgBase64 = Base64.getEncoder().encodeToString(file.getBytes());
            AITextResponse aiText = new AITextResponse();
            aiText = tencentAi.imageRecognition(imgBase64, 1L);

            // 上传文件路径
            String filePath = FriendBetterConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileUrl = FileUploadUtils.upload(filePath, file);

            String imagesStr = aiText.getTextStr().replaceAll("\\s", "");
            log.info("{} 扫描时间数据 ----> {} ", DateUtil.now(), aiText.getTimeList());
            List<Long> repertoireIdList = repertoireInfoDetailService.findRepertoireByTime(aiText.getTimeList());

            ImageScanRecord imageScanRecord = new ImageScanRecord();
            imageScanRecord.setFileUrl(fileUrl);
            imageScanRecord.setDeviceModel(deviceModel);
            imageScanRecord.setWordList(aiText.getText());
            imageScanRecord.setBody(aiText.getBody());
            imageScanRecord.setRequestId(aiText.getRequestId());

            // 如果无剧目信息，则录入新剧目
            if (CollectionUtils.isEmpty(repertoireIdList)) {
                imageScanRecordService.add(imageScanRecord);

                // deepseek准确识别票面信息
                DeepSeekAi deepSeekAi = new DeepSeekAi();
                String ticketText = aiText.getTextStr();

                log.info("deepseek识别票面信息: {}", ticketText);
                AITextResponse deepSeekAiText = deepSeekAi.recognition(ticketText);

            }

            PortfolioResponse portfolio = portfolioService.findPortfolioByImageText(imagesStr, repertoireIdList);

            if (portfolio == null) {
                imageScanRecordService.add(imageScanRecord);
                log.error("无商品信息-商品问题: {}", imagesStr);

                // 有剧目无藏品

                return R.fail("本场演出没有进入演都， 请联系客服帮助我们完善信息~谢谢您（客服联系方式在 我的-系统公告中）");
            }
            // 查询是否有该场次
            RepertoireInfoDetail repertoireInfoDetail = repertoireInfoDetailService.findRepertoireInfoDetail(
                    portfolio.getTheaterId(),
                    portfolio.getRepertoireId(),
                    aiText.getTimeList());

            // 判断是否为默认识别
            if (portfolio.getScanningId() != null && portfolio.getScanningId() > 1L) {
                aiText = tencentAi.imageRecognition(imgBase64, portfolio.getScanningId());
                imageScanRecord.setRequestId1(aiText.getRequestId());
                imageScanRecord.setWordList1(imagesStr);
                imageScanRecord.setBody1(aiText.getBody());
            }

            imageScanRecord.setPortfolioId(portfolio.getId());
            imageScanRecordService.add(imageScanRecord);

            // 判断是否票据是否被扫描
            if (StatusFlag.OK.getCode().equals(portfolio.getSeatStatus())) {
                if (StringUtils.isBlank(aiText.getSeat())) {
                    return R.fail("座位不能为空，请补充完整座位号");
                }
                Long count = userReceivingRecordsService.findIsPickedUp(
                        portfolio.getId(),
                        aiText.getSeat(),
                        aiText.getTimeList());
                if (count > 0) {
                    return R.fail("当前纸质票的电子收藏票已被领取");
                }
            }

            PortfolioInfo portfolioInfo = portfolioInfoService.getById(portfolio.getPortfolioId());
            Repertoire repertoire = repertoireService.getById(portfolioInfo.getRepertoireId());
            RepertoireTicket repertoireTicket = repertoireTicketService.getById(portfolioInfo.getRepertoireTicketId());

            // 查询电子票信息
            long exist = userReceivingRecordsService.count(new LambdaQueryWrapper<UserReceivingRecords>()
                    .eq(UserReceivingRecords::getUserId, SecurityUtils.getUserId())
                    .eq(UserReceivingRecords::getPortfolioId, portfolio.getId())
                    .eq(UserReceivingRecords::getRelationId, portfolio.getRepertoireTicketId())
                    .eq(UserReceivingRecords::getRepertoireInfoId, repertoireInfoDetail.getRepertoireInfoId())
                    .eq(UserReceivingRecords::getRepertoireInfoDetailId, repertoireInfoDetail.getId())
                    .eq(UserReceivingRecords::getBadgeType, BadgeTypeFlag.ELECTRONIC_TICKET.getCode()));
            if (exist > 0) {
                return R.fail("同场演出，无法重复领取电子票");
            }

            aiText.setNo(portfolio.getNo());
            aiText.setRepertoire(portfolio.getRepertoireName());
            aiText.setTheater(portfolio.getTheaterName());
            aiText.setCommonImage(portfolioInfo.getCommonImage());
            aiText.setCoverFront(portfolioInfo.getCoverFront());
            aiText.setCoverReverse(portfolioInfo.getCoverReverse());
            aiText.setDigitalAvatarCommonImage(portfolioInfo.getDigitalAvatarCommonImage());
            aiText.setDateTime(
                    DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, repertoireInfoDetail.getStartTime()));
            aiText.setPortfolioId(portfolio.getId());
            aiText.setPortfolioInfoId(portfolio.getPortfolioId());
            aiText.setTheaterId(portfolio.getTheaterId());
            aiText.setRepertoireId(portfolio.getRepertoireId());
            aiText.setRepertoireInfoId(repertoireInfoDetail.getRepertoireInfoId());
            aiText.setRepertoireInfoDetailId(repertoireInfoDetail.getId());
            aiText.setRepertoireCoverPicture(repertoire.getCoverPicture());
            aiText.setRepertoireTicketId(portfolioInfo.getRepertoireTicketId());
            aiText.setDigitalAvatarId(portfolioInfo.getDigitalAvatarId());

            // 查询用户扫描次数
            Long number = imageScanRecordService.findPortfolioCount(portfolio.getId());
            aiText.setSerialNumber(formatNumber(number + 1));

            // 查询数字头像
            if (StringUtils.isNotBlank(repertoireTicket.getCoverFront()) && portfolioInfo.getDigitalAvatarId() != null
                    && portfolioInfo.getDigitalAvatarId() > 0) {
                Long digitalAvatarCount = userDigitalAvatarService.count(new LambdaQueryWrapper<UserDigitalAvatar>()
                        .eq(UserDigitalAvatar::getDigitalAvatarId, portfolioInfo.getDigitalAvatarId())
                        .eq(UserDigitalAvatar::getPortfolioId, portfolio.getId()));
                List<String> digitalAvatarUrl = digitalAvatarService
                        .findDigitalAvatar(portfolioInfo.getDigitalAvatarId(), digitalAvatarCount + 2);
                aiText.setDigitalAvatarUrlList(digitalAvatarUrl);
            }
            aiText.setFileUrl(fileUrl);
            aiText.setPrice(extractDigitsFromPriceString(aiText.getPrice()));
            return R.ok(aiText);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 新增纸质票扫描API - 智能创建剧目和场次
     */
    @PostMapping(value = "/newTicketRecognition", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @Anonymous
    public R<AITextResponse> newTicketRecognition(String deviceModel, MultipartFile file) {
        String imgBase64 = "";
        try {
            imgBase64 = Base64.getEncoder().encodeToString(file.getBytes());
            AITextResponse aiText = new AITextResponse();
            aiText = tencentAi.imageRecognition(imgBase64, 1L);

            // 上传文件路径
            String filePath = FriendBetterConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileUrl = FileUploadUtils.upload(filePath, file);

            String imagesStr = aiText.getTextStr().replaceAll("\\s", "");
            log.info("{} 新扫描识别数据 ----> {} ", DateUtil.now(), aiText.getTimeList());

            // 1. 判断数据库中是否有相同剧目
            Long repertoireId = null;
            Long theaterId = null;
            Long repertoireInfoId = null;
            RepertoireInfoDetail repertoireInfoDetail = null;

            List<Long> repertoireList = findRepertoireByMatch(imagesStr);
            AITextResponse deepSeekAiText = null;

            // 多个条件下需要进一步deepseek分析：aiText中的seat为空，匹配到非1个剧目，则需要使用deepseek深入识别
            if (StringUtil.isEmpty(aiText.getSeat()) || StringUtils.isBlank(aiText.getTheater())
                    || repertoireList == null
                    || (repertoireList != null && repertoireList.size() > 1)) {
                // deepseek准确识别票面信息
                // DeepSeekAi deepSeekAi = new DeepSeekAi();
                AliyunAi deepSeekAi = new AliyunAi();
                String ticketText = aiText.getTextStr();

                log.info("deepseek识别票面信息: {}", ticketText);
                deepSeekAiText = deepSeekAi.recognition(ticketText);
                log.info("deepseek识别票面信息结果: {}", deepSeekAiText.toString());

                if (StringUtils.isBlank(deepSeekAiText.getRepertoire())) {
                    return R.fail("剧目识别失败，请检查图片是否清晰");
                }

                CopyOptions options = CopyOptions.create()
                        .setIgnoreNullValue(true) // 忽略源对象属性为空的情况
                        .setIgnoreError(true);
                BeanUtil.copyProperties(deepSeekAiText, aiText, options);

            }

            // 如果有多个剧目，则需要筛选一次，找出最符合的,筛选过程中同时尝试定位剧场
            if (repertoireList != null && repertoireList.size() > 1 && deepSeekAiText != null) {
                // 通过剧场名称筛选
                for (Long comparedRepertoireId : repertoireList) {
                    List<RepertoireInfoResponse> repertoireTheaterInfoList = repertoireInfoService
                            .findRepertoireTheaterInfo(comparedRepertoireId);

                    if (CollectionUtils.isEmpty(repertoireTheaterInfoList)) {
                        continue;
                    }

                    if (repertoireTheaterInfoList.size() > 0) {
                        String sourceTheater = deepSeekAiText.getTheater();
                        if (StringUtils.isBlank(sourceTheater)) {
                            continue;
                        }

                        for (RepertoireInfoResponse repertoireInfo : repertoireTheaterInfoList) {
                            String comparedTheater = repertoireInfo.getTheaterName();
                            double matchDegree = StringUtils.matchDegree(sourceTheater, comparedTheater);
                            if (matchDegree >= 0.8) {
                                repertoireId = comparedRepertoireId;
                                if (repertoireInfo.getTheaterId() != null) {
                                    theaterId = Long.parseLong(repertoireInfo.getTheaterId());
                                }
                                break;
                            }
                        }
                    }
                }
                // 如果没有匹配到剧目剧场完全一致的，则先将第一个剧目作为目标
                if (repertoireId == null) {
                    repertoireId = repertoireList.get(0);
                }
            } else if (repertoireList != null && repertoireList.size() == 1) {
                repertoireId = repertoireList.get(0);
            }

            // 2.查找剧场
            Theater theater = theaterService.findBestMatchTheater(aiText.getTheater());
            if (theater != null)
                theaterId = theater.getId();

            // 3.创建剧目
            if (repertoireId == null) {
                // 创建新剧目
                Repertoire repertoire = new Repertoire();
                repertoire
                        .setName(StringUtils.isNotBlank(aiText.getRepertoire()) ? aiText.getRepertoire()
                                : "未知剧目");
                repertoire.setIntroduction("");
                repertoire.setRating(0.0);
                repertoire.setRecommend(0);
                repertoire.setGoodRatingRate(0.0);
                repertoire.setCoverPicture(fileUrl);// 使用扫票票面作为封面图
                repertoire.setFocusNumber(0);
                repertoire.setAudit(2); // 直接通过审核
                repertoire.setAuditPassTime(new Date());
                repertoire.setShortName(aiText.getShortRepertoireName());
                repertoire.setStatus(1);
                repertoire.setDeleted(1);
                repertoireService.save(repertoire);

                log.info("创建新剧目: {}, ID: {}", repertoire.getName(), repertoireId);
                repertoireId = repertoire.getId();
            }
            // 4.创建剧场
            if (theaterId == null) {
                theater = new Theater();
                theater.setName(
                        StringUtils.isNotBlank(aiText.getTheater()) ? aiText.getTheater() : "未知剧场");
                theater.setShortName(aiText.getShortTheaterName());
                theater.setAddress(aiText.getAddress());
                theater.setRemark("AI识别自动创建");
                theater.setAudit(2); // 直接通过审核
                theater.setAuditPassTime(new Date());
                theater.setStatus(1);
                theater.setDeleted(1);
                theaterService.save(theater);
                log.info("创建新剧场: {}, ID: {}", theater.getName(), theater.getId());

                theaterId = theater.getId();
            }

            // 5.创建剧目剧场关联
            if (repertoireId != null && theaterId != null) {
                // 从剧目信息表中查找该剧目的剧场信息
                RepertoireInfo repertoireInfo = repertoireInfoService.getOne(
                        new LambdaQueryWrapper<RepertoireInfo>()
                                .eq(RepertoireInfo::getRepertoireId, repertoireId)
                                .eq(RepertoireInfo::getTheaterId, theaterId)
                                .last("limit 1"));
                if (repertoireInfo == null) {
                    // 创建剧目剧场关联
                    repertoireInfo = new RepertoireInfo();
                    repertoireInfo.setTheaterId(theaterId);
                    repertoireInfo.setRepertoireId(repertoireId);
                    repertoireInfo.setTemporaryFlag(1); // 临时标记
                    repertoireInfo.setTheaterPass(1);
                    repertoireInfo.setRepertoirePass(1);
                    repertoireInfo.setStatus(1);
                    repertoireInfo.setAudit(2);

                    repertoireInfoService.save(repertoireInfo);

                    repertoireInfoId = repertoireInfo.getId();
                }
            }

            // 6.创建剧目剧场场次

            repertoireInfoDetail = repertoireInfoDetailService.findRepertoireInfoDetail(
                    theaterId, repertoireId, aiText.getTimeList());

            if (repertoireInfoDetail == null) {
                // 设置演出时间
                Date showTime = null;
                if (CollectionUtils.isNotEmpty(aiText.getTimeList())) {
                    try {
                        // 尝试解析时间
                        for (String timeStr : aiText.getTimeList()) {
                            if (timeStr.length() >= 12) {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
                                showTime = sdf.parse(timeStr);
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.warn("解析时间失败，使用当前时间: {}", e.getMessage());
                    }
                }

                if (showTime != null) {
                    repertoireInfoDetail = new RepertoireInfoDetail();
                    repertoireInfoDetail.setRepertoireInfoId(repertoireInfoId);
                    repertoireInfoDetail.setTheaterId(theaterId);
                    repertoireInfoDetail.setRepertoireId(repertoireId);
                    repertoireInfoDetail.setStartTime(showTime);
                    repertoireInfoDetail.setEndTime(new Date(showTime.getTime() + 2 * 60 * 60 * 1000)); // 默认2小时后结束
                    // createBy, updateBy, createTime, updateTime 字段由 MyBatis-Plus 自动填充
                    repertoireInfoDetailService.save(repertoireInfoDetail);
                    log.info("创建新场次: 剧目ID={}, 剧场ID={}, 时间={}", repertoireId, theaterId, showTime);
                }

            } else {
                log.info("找到已有场次: 场次ID={}", repertoireInfoDetail.getId());
            }

            // 7. 检查用户是否已经领取过该场次的电子票
            if (repertoireInfoDetail != null) {
                long exist = userReceivingRecordsService.count(new LambdaQueryWrapper<UserReceivingRecords>()
                        .eq(UserReceivingRecords::getUserId, SecurityUtils.getUserId())
                        .eq(UserReceivingRecords::getRepertoireId, repertoireId)
                        .eq(UserReceivingRecords::getTheaterId, theaterId)
                        .eq(UserReceivingRecords::getRepertoireInfoDetailId, repertoireInfoDetail.getId())
                        .eq(UserReceivingRecords::getBadgeType, BadgeTypeFlag.ELECTRONIC_TICKET.getCode()));

                if (exist > 0) {
                    return R.fail("您已经领取过同场演出的电子票，无法重复领取");
                }
            }

            // 8. 查找关联的Portfolio信息或使用随机票面
            Portfolio portfolio = portfolioService.findPortfolio(theaterId, repertoireId);
            PortfolioInfo portfolioInfo = null;
            RepertoireTicket repertoireTicket = null;

            if (portfolio != null) {
                // 使用关联的Portfolio
                portfolioInfo = portfolioInfoService.getByPortfolioId(portfolio.getId());
                log.info("使用关联Portfolio: ID={}", portfolio.getId());
            } else {
                // 使用随机Portfolio
                List<Portfolio> randomTickets = portfolioService.getRandomTicketPortfolios();
                if (CollectionUtils.isNotEmpty(randomTickets)) {
                    portfolio = randomTickets.get((int) (Math.random() * randomTickets.size()));
                    portfolio.setIfCompositeRepertoireName(true);// 随机票一定要合成剧目名称
                    portfolioInfo = portfolioInfoService.getByPortfolioId(portfolio.getId());
                    log.info("使用随机Portfolio: ID={}", portfolio.getId());
                } else {
                    return R.fail("暂无可用随机票面，请联系管理员");
                }
            }

            if (portfolioInfo.getRepertoireTicketId() != null)
                repertoireTicket = repertoireTicketService.getById(portfolioInfo.getRepertoireTicketId());

            // 构建返回结果，参考testImgRecognition方法
            Repertoire repertoire = repertoireService.getById(repertoireId);
            if (repertoire == null) {
                return R.fail("剧目信息不存在");
            }
            aiText.setNo(portfolioInfo.getNo() != null ? portfolioInfo.getNo()
                    : repertoire.getName() + "-" + System.currentTimeMillis());
            aiText.setRepertoire(repertoire.getName());
            aiText.setTheater(theaterService.getById(theaterId).getName());
            aiText.setCommonImage(portfolioInfo.getCommonImage());
            aiText.setCoverFront(portfolioInfo.getCoverFront());
            aiText.setCoverReverse(portfolioInfo.getCoverReverse());
            aiText.setDigitalAvatarCommonImage(portfolioInfo.getDigitalAvatarCommonImage());
            aiText.setPortfolioId(portfolio.getId());
            aiText.setPortfolioInfoId(portfolioInfo.getId());
            aiText.setTheaterId(theaterId);
            aiText.setRepertoireId(repertoireId);
            aiText.setRepertoireInfoId(repertoireInfoId);
            aiText.setRepertoireInfoDetailId(repertoireInfoDetail != null ? repertoireInfoDetail.getId() : null);
            aiText.setRepertoireCoverPicture(repertoire.getCoverPicture());
            aiText.setRepertoireTicketId(portfolioInfo.getRepertoireTicketId());
            aiText.setDigitalAvatarId(portfolioInfo.getDigitalAvatarId());
            aiText.setIfCompositeRepertoireName(portfolio.getIfCompositeRepertoireName());

            // 查询用户扫描次数
            Long number = imageScanRecordService.findPortfolioCount(portfolio.getId());
            aiText.setSerialNumber(formatNumber(number + 1));

            // 查询数字头像
            if (repertoireTicket != null && StringUtils.isNotBlank(repertoireTicket.getCoverFront())
                    && portfolioInfo.getDigitalAvatarId() != null
                    && portfolioInfo.getDigitalAvatarId() > 0) {
                Long digitalAvatarCount = userDigitalAvatarService.count(new LambdaQueryWrapper<UserDigitalAvatar>()
                        .eq(UserDigitalAvatar::getDigitalAvatarId, portfolioInfo.getDigitalAvatarId())
                        .eq(UserDigitalAvatar::getPortfolioId, portfolio.getId()));
                List<String> digitalAvatarUrl = digitalAvatarService
                        .findDigitalAvatar(portfolioInfo.getDigitalAvatarId(), digitalAvatarCount + 2);
                aiText.setDigitalAvatarUrlList(digitalAvatarUrl);
            }
            aiText.setFileUrl(fileUrl);
            aiText.setPrice(extractDigitsFromPriceString(aiText.getPrice()));
            log.info("aiText: {}", aiText.toString());

            // 9.开新线程保存记录扫描记录

            // 异步保存扫描记录，按照项目中 AsyncFactory 的最佳实践
            AsyncManager.me().execute(createImageScanRecordTask(aiText, fileUrl, deviceModel, imagesStr));

            return R.ok(aiText);

        } catch (IOException e) {
            log.error("文件处理失败", e);
            throw new RuntimeException("文件处理失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("票据识别处理失败", e);
            return R.fail("票据识别处理失败: " + e.getMessage());
        }
    }

    public String formatNumber(Long number) {
        String formattedNumber = String.format("NO.%05d", number);
        return formattedNumber;
    }

    /**
     * 提取字符串中的数字
     *
     * @param priceStr
     * @return
     */
    public String extractDigitsFromPriceString(String priceStr) {
        if (StringUtils.isNotBlank(priceStr)) {
            String price = UserReceivingRecordsServiceImpl.extractDigits(priceStr);
            if (ZERO.equals(price)) {
                return ZERO;
            }
            if (MAX_PRICE.compareTo(new BigDecimal(price)) <= 0) {
                return ZERO;
            }
            List<TicketKey> keyList = ticketKeyService.list(new LambdaQueryWrapper<TicketKey>()
                    .eq(TicketKey::getStatus, StatusFlag.OK.getCode()));
            if (CollectionUtils.isNotEmpty(keyList)) {
                for (TicketKey ticketKey : keyList) {
                    if (priceStr.contains(ticketKey.getValue())) {
                        return ZERO;
                    }
                }
            }
            return price;
        }
        return ZERO;
    }

    /**
     * 基于Redis缓存和匹配算法查找剧目（超高性能优化版本）
     *
     * @param imagesStr 票面字符
     * @return 匹配的剧目对象
     */
    private List<Long> findRepertoireByMatch(String imagesStr) {
        if (StringUtils.isBlank(imagesStr)) {
            return null;
        }

        // 从Redis缓存中获取剧目列表
        List<RepertoireCacheInfo> repertoireList = getRepertoireListFromCache();

        // 如果缓存为空，则从数据库加载
        if (CollectionUtils.isEmpty(repertoireList)) {
            repertoireList = loadRepertoireListToCache();
        }

        // 使用高性能优化器进行匹配
        List<Long> result = repertoireMatchOptimizer.findMatchingRepertoires(imagesStr, repertoireList);

        if (result != null && !result.isEmpty()) {
            log.info("找到匹配的剧目: {}", result.size());
            return result;
        }

        log.info("未找到匹配的剧目, 票面字符: {}", imagesStr);
        return null;
    }

    /**
     * 从Redis缓存中获取剧目列表
     * 
     * @return 剧目缓存信息列表
     */
    private List<RepertoireCacheInfo> getRepertoireListFromCache() {
        String cacheKey = "repertoire_cache_list";
        List<RepertoireCacheInfo> repertoireList = redisCache.getCacheList(cacheKey);
        return repertoireList != null ? repertoireList : new ArrayList<>();
    }

    /**
     * 从数据库加载剧目列表到Redis缓存
     * 
     * @return 剧目缓存信息列表
     */
    private List<RepertoireCacheInfo> loadRepertoireListToCache() {
        String cacheKey = "repertoire_cache_list";

        // 查询所有剧目，获取id和shortName或name
        List<Repertoire> allRepertoires = repertoireService.list(
                new LambdaQueryWrapper<Repertoire>()
                        .select(Repertoire::getId, Repertoire::getName, Repertoire::getShortName)
                        .eq(Repertoire::getStatus, 1)
                        .eq(Repertoire::getDeleted, 1)
                        .eq(Repertoire::getAudit, 2));

        List<RepertoireCacheInfo> repertoireList = new ArrayList<>();
        for (Repertoire repertoire : allRepertoires) {
            // 如果shortName为空使用name代替
            String displayName = StringUtils.isNotBlank(repertoire.getShortName())
                    ? repertoire.getShortName()
                    : repertoire.getName();

            if (StringUtils.isNotBlank(displayName)) {
                repertoireList.add(new RepertoireCacheInfo(repertoire.getId(), displayName));
            }
        }

        // 存入Redis缓存，设置过期时间为1000小时
        if (CollectionUtils.isNotEmpty(repertoireList)) {
            redisCache.setCacheList(cacheKey, repertoireList);
            redisCache.expire(cacheKey, 3600000); // 1000小时过期
            log.info("剧目列表已缓存到Redis，共{}条记录", repertoireList.size());
        }

        return repertoireList;
    }

    /**
     * 手动重置剧目匹配索引API
     */
    @PostMapping("/resetIndex")
    @Anonymous
    public R<String> resetMatchIndex() {
        try {
            repertoireMatchOptimizer.resetIndex();
            return R.ok("索引重置成功");
        } catch (Exception e) {
            log.error("重置索引失败", e);
            return R.fail("重置索引失败: " + e.getMessage());
        }
    }

    /**
     * 创建异步保存扫描记录的任务
     * 按照项目中 AsyncFactory 的最佳实践，在主线程中获取所需信息，传递给异步任务
     *
     * @param aiTextResponse AI识别结果
     * @param fileUrl        文件URL
     * @param deviceModel    设备型号
     * @param imagesStr      图片字符串
     * @return TimerTask
     */
    private TimerTask createImageScanRecordTask(final AITextResponse aiTextResponse, final String fileUrl,
            final String deviceModel, final String imagesStr) {
        // 在主线程中获取用户信息和IP地址
        final Long currentUserId = SecurityUtils.getUserId();
        final String currentIpAddress = com.youying.common.utils.ip.IpUtils.getIpAddr();

        return new TimerTask() {
            @Override
            public void run() {
                try {
                    ImageScanRecord imageScanRecord = new ImageScanRecord();
                    imageScanRecord.setFileUrl(fileUrl);
                    imageScanRecord.setDeviceModel(deviceModel);
                    imageScanRecord.setWordList(aiTextResponse.getText());
                    imageScanRecord.setBody(aiTextResponse.getBody());
                    imageScanRecord.setWordList1(imagesStr);
                    imageScanRecord.setRequestId(aiTextResponse.getRequestId());
                    imageScanRecord.setPortfolioId(aiTextResponse.getPortfolioId());
                    imageScanRecord.setFinalResult(com.alibaba.fastjson.JSON.toJSONString(aiTextResponse));
                    // 使用主线程中获取的用户信息
                    imageScanRecord.setUserId(currentUserId);
                    imageScanRecord.setIpAddress(currentIpAddress);
                    imageScanRecord.setCreateTime(new Date());

                    // 保存记录
                    imageScanRecordService.save(imageScanRecord);
                    log.info("异步保存扫描记录成功，用户ID: {}", currentUserId);
                } catch (Exception e) {
                    log.error("异步保存扫描记录失败: {}", e.getMessage(), e);
                }
            }
        };
    }

}
