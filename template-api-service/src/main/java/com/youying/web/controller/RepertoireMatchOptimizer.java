package com.youying.web.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.youying.common.utils.StringUtils;
import com.youying.system.domain.repertoire.RepertoireCacheInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 剧目匹配性能优化器
 * 
 * 提供多种优化策略：
 * 1. 前缀索引优化
 * 2. 长度分组优化
 * 3. 结果缓存优化
 * 4. 并行计算优化
 * 
 * <AUTHOR>
 * @since 2025-01-07
 */
@Component
@Slf4j
public class RepertoireMatchOptimizer {

    // 前缀索引，用于快速定位可能匹配的剧目
    private static final Map<String, Set<Long>> PREFIX_INDEX = new ConcurrentHashMap<>();

    // 长度分组索引，按剧目名称长度分组
    private static final Map<Integer, List<RepertoireCacheInfo>> LENGTH_GROUP_INDEX = new ConcurrentHashMap<>();

    // 匹配结果缓存
    private static final Map<String, List<Long>> MATCH_RESULT_CACHE = new ConcurrentHashMap<>();
    private static final Map<String, Long> CACHE_TIMESTAMP = new ConcurrentHashMap<>();
    private static final long CACHE_EXPIRE_TIME = 30 * 60 * 1000; // 30分钟过期

    // 索引是否已构建
    private static volatile boolean indexBuilt = false;

    // 记录上次构建索引时的数据版本（基于数据大小和哈希值）
    private static volatile int lastDataVersion = -1;

    /**
     * 构建索引以提升匹配性能
     */
    public void buildIndex(List<RepertoireCacheInfo> repertoireList) {
        if (CollectionUtils.isEmpty(repertoireList)) {
            return;
        }

        log.info("开始构建剧目匹配索引，剧目数量: {}", repertoireList.size());
        long startTime = System.currentTimeMillis();

        // 清空现有索引
        PREFIX_INDEX.clear();
        LENGTH_GROUP_INDEX.clear();

        // 构建前缀索引和长度分组索引
        for (RepertoireCacheInfo repertoire : repertoireList) {
            String name = repertoire.getName();
            if (StringUtils.isBlank(name)) {
                continue;
            }

            // 构建前缀索引（1-3字符前缀）
            buildPrefixIndex(name, repertoire.getId());

            // 构建长度分组索引
            buildLengthGroupIndex(name, repertoire);
        }

        indexBuilt = true;
        long endTime = System.currentTimeMillis();
        log.info("剧目匹配索引构建完成，耗时: {}ms, 前缀索引条目: {}, 长度分组: {}",
                endTime - startTime, PREFIX_INDEX.size(), LENGTH_GROUP_INDEX.size());
    }

    /**
     * 构建前缀索引
     */
    private void buildPrefixIndex(String name, Long repertoireId) {
        // 为1-3字符的前缀建立索引
        for (int len = 1; len <= Math.min(3, name.length()); len++) {
            String prefix = name.substring(0, len);
            PREFIX_INDEX.computeIfAbsent(prefix, k -> new HashSet<>()).add(repertoireId);
        }
    }

    /**
     * 构建长度分组索引
     */
    private void buildLengthGroupIndex(String name, RepertoireCacheInfo repertoire) {
        int length = name.length();
        LENGTH_GROUP_INDEX.computeIfAbsent(length, k -> new ArrayList<>()).add(repertoire);
    }

    /**
     * 高性能剧目匹配（支持自动索引更新）
     */
    public List<Long> findMatchingRepertoires(String imagesStr, List<RepertoireCacheInfo> repertoireList) {
        if (StringUtils.isBlank(imagesStr)) {
            return null;
        }

        // 检查数据是否发生变化，如果变化则重建索引
        int currentDataVersion = calculateDataVersion(repertoireList);
        if (!indexBuilt || currentDataVersion != lastDataVersion) {
            log.info("检测到数据变化，重建索引。当前版本: {}, 上次版本: {}", currentDataVersion, lastDataVersion);
            buildIndex(repertoireList);
            lastDataVersion = currentDataVersion;

            // 数据变化时清空匹配结果缓存
            clearMatchResultCache();
        }

        // 检查缓存
        String cacheKey = imagesStr.trim();
        List<Long> cachedResult = getCachedResult(cacheKey);
        if (cachedResult != null) {
            log.debug("从缓存中获取匹配结果: {}", cachedResult.size());
            return cachedResult;
        }

        List<Long> result = performOptimizedMatch(imagesStr, repertoireList);

        // 缓存结果
        setCachedResult(cacheKey, result);

        return result;
    }

    /**
     * 计算数据版本（基于数据大小和内容哈希）
     */
    private int calculateDataVersion(List<RepertoireCacheInfo> repertoireList) {
        if (CollectionUtils.isEmpty(repertoireList)) {
            return 0;
        }

        // 使用数据大小和前几个元素的哈希值作为版本标识
        int version = repertoireList.size();

        // 取前10个和后10个元素计算哈希，避免遍历全部数据影响性能
        int sampleSize = Math.min(10, repertoireList.size());

        // 前10个元素
        for (int i = 0; i < sampleSize; i++) {
            RepertoireCacheInfo item = repertoireList.get(i);
            if (item != null && item.getName() != null) {
                version = version * 31 + item.getName().hashCode();
                if (item.getId() != null) {
                    version = version * 31 + item.getId().hashCode();
                }
            }
        }

        // 后10个元素（如果数据量大于10）
        if (repertoireList.size() > 10) {
            int startIndex = repertoireList.size() - sampleSize;
            for (int i = startIndex; i < repertoireList.size(); i++) {
                RepertoireCacheInfo item = repertoireList.get(i);
                if (item != null && item.getName() != null) {
                    version = version * 31 + item.getName().hashCode();
                    if (item.getId() != null) {
                        version = version * 31 + item.getId().hashCode();
                    }
                }
            }
        }

        return version;
    }

    /**
     * 清空匹配结果缓存
     */
    private void clearMatchResultCache() {
        MATCH_RESULT_CACHE.clear();
        CACHE_TIMESTAMP.clear();
        log.info("已清空匹配结果缓存");
    }

    /**
     * 执行优化的匹配算法
     */
    private List<Long> performOptimizedMatch(String imagesStr, List<RepertoireCacheInfo> repertoireList) {
        // 策略1: 完全包含匹配（最快）
        List<Long> exactMatches = findExactMatches(repertoireList, imagesStr);
        if (!exactMatches.isEmpty()) {
            log.info("找到完全匹配的剧目: {}", exactMatches.size());
            return exactMatches;
        }

        // 策略2: 使用前缀索引快速筛选候选项
        Set<Long> candidates = getCandidatesByPrefix(imagesStr);
        if (!candidates.isEmpty()) {
            List<RepertoireCacheInfo> candidateList = repertoireList.stream()
                    .filter(r -> candidates.contains(r.getId()))
                    .collect(Collectors.toList());

            List<Long> prefixMatches = performDetailedMatch(candidateList, imagesStr);
            if (!prefixMatches.isEmpty()) {
                log.info("通过前缀索引找到匹配的剧目: {}", prefixMatches.size());
                return prefixMatches;
            }
        }

        // 策略3: 使用长度分组优化匹配
        List<Long> lengthBasedMatches = performLengthBasedMatch(imagesStr);
        if (!lengthBasedMatches.isEmpty()) {
            log.info("通过长度分组找到匹配的剧目: {}", lengthBasedMatches.size());
            return lengthBasedMatches;
        }

        // 策略4: 并行全量匹配（最后的选择）
        return performParallelFullMatch(repertoireList, imagesStr);
    }

    /**
     * 完全匹配检查
     */
    private List<Long> findExactMatches(List<RepertoireCacheInfo> repertoireList, String imagesStr) {
        return repertoireList.stream()
                .filter(cacheInfo -> imagesStr.contains(cacheInfo.getName()))
                .map(RepertoireCacheInfo::getId)
                .collect(Collectors.toList());
    }

    /**
     * 通过前缀索引获取候选项
     */
    private Set<Long> getCandidatesByPrefix(String imagesStr) {
        Set<Long> candidates = new HashSet<>();

        // 检查输入字符串的前缀是否在索引中
        for (int len = 1; len <= Math.min(3, imagesStr.length()); len++) {
            String prefix = imagesStr.substring(0, len);
            Set<Long> prefixCandidates = PREFIX_INDEX.get(prefix);
            if (prefixCandidates != null) {
                candidates.addAll(prefixCandidates);
            }
        }

        return candidates;
    }

    /**
     * 对候选项进行详细匹配
     */
    private List<Long> performDetailedMatch(List<RepertoireCacheInfo> candidates, String imagesStr) {
        return candidates.parallelStream()
                .filter(cacheInfo -> {
                    double matchDegree = StringUtils.matchDegree(cacheInfo.getName(), imagesStr);
                    if (matchDegree >= 0.8) {
                        log.debug("剧目匹配成功: {} -> {}, 匹配度: {}", cacheInfo.getName(), imagesStr, matchDegree);
                        return true;
                    }
                    return false;
                })
                .map(RepertoireCacheInfo::getId)
                .collect(Collectors.toList());
    }

    /**
     * 基于长度分组的匹配
     */
    private List<Long> performLengthBasedMatch(String imagesStr) {
        int targetLength = imagesStr.length();
        List<Long> matches = new ArrayList<>();

        // 检查相似长度的剧目（±20%长度范围内）
        int minLength = (int) (targetLength * 0.8);
        int maxLength = (int) (targetLength * 1.2);

        for (int length = minLength; length <= maxLength; length++) {
            List<RepertoireCacheInfo> group = LENGTH_GROUP_INDEX.get(length);
            if (group != null) {
                List<Long> groupMatches = performDetailedMatch(group, imagesStr);
                matches.addAll(groupMatches);
            }
        }

        return matches;
    }

    /**
     * 并行全量匹配
     */
    private List<Long> performParallelFullMatch(List<RepertoireCacheInfo> repertoireList, String imagesStr) {
        return repertoireList.parallelStream()
                .filter(cacheInfo -> {
                    // 长度预筛选
                    int lengthDiff = Math.abs(imagesStr.length() - cacheInfo.getName().length());
                    if (lengthDiff > Math.max(imagesStr.length(), cacheInfo.getName().length()) * 0.8) {
                        return false;
                    }

                    double matchDegree = StringUtils.matchDegree(cacheInfo.getName(), imagesStr);
                    if (matchDegree >= 0.8) {
                        log.debug("剧目匹配成功: {} -> {}, 匹配度: {}", cacheInfo.getName(), imagesStr, matchDegree);
                        return true;
                    }
                    return false;
                })
                .map(RepertoireCacheInfo::getId)
                .collect(Collectors.toList());
    }

    /**
     * 获取缓存结果
     */
    private List<Long> getCachedResult(String cacheKey) {
        Long timestamp = CACHE_TIMESTAMP.get(cacheKey);
        if (timestamp != null && System.currentTimeMillis() - timestamp < CACHE_EXPIRE_TIME) {
            return MATCH_RESULT_CACHE.get(cacheKey);
        }
        // 清理过期缓存
        if (timestamp != null) {
            MATCH_RESULT_CACHE.remove(cacheKey);
            CACHE_TIMESTAMP.remove(cacheKey);
        }
        return null;
    }

    /**
     * 设置缓存结果
     */
    private void setCachedResult(String cacheKey, List<Long> result) {
        // 防止缓存过大
        if (MATCH_RESULT_CACHE.size() > 1000) {
            clearExpiredCache();
        }

        MATCH_RESULT_CACHE.put(cacheKey, result);
        CACHE_TIMESTAMP.put(cacheKey, System.currentTimeMillis());
    }

    /**
     * 清理过期缓存
     */
    private void clearExpiredCache() {
        long currentTime = System.currentTimeMillis();
        CACHE_TIMESTAMP.entrySet().removeIf(entry -> {
            boolean expired = currentTime - entry.getValue() >= CACHE_EXPIRE_TIME;
            if (expired) {
                MATCH_RESULT_CACHE.remove(entry.getKey());
            }
            return expired;
        });
    }

    /**
     * 重置索引（当剧目数据更新时调用）
     */
    public void resetIndex() {
        PREFIX_INDEX.clear();
        LENGTH_GROUP_INDEX.clear();
        MATCH_RESULT_CACHE.clear();
        CACHE_TIMESTAMP.clear();
        indexBuilt = false;
        lastDataVersion = -1; // 重置数据版本
        log.info("剧目匹配索引已重置");
    }

    /**
     * 获取当前索引状态信息（用于调试和监控）
     */
    public Map<String, Object> getIndexStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("indexBuilt", indexBuilt);
        status.put("lastDataVersion", lastDataVersion);
        status.put("prefixIndexSize", PREFIX_INDEX.size());
        status.put("lengthGroupIndexSize", LENGTH_GROUP_INDEX.size());
        status.put("matchResultCacheSize", MATCH_RESULT_CACHE.size());
        return status;
    }
}
