package com.youying.web.controller;

import javax.validation.Valid;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserReminder;
import com.youying.system.domain.userreminder.TheaterFundSettings;
import com.youying.system.domain.userreminder.UserReminderRequest;
import com.youying.system.domain.userreminder.UserReminderResponse;
import com.youying.system.service.UserReminderService;

/**
 * 用户提醒设置控制器
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@RestController
@RequestMapping("/userReminder")
public class UserReminderController extends BaseController<UserReminder> {

    @Autowired
    private UserReminderService userReminderService;

    /**
     * 获取用户提醒设置
     *
     * @return 用户提醒设置
     */
    @GetMapping("/getUserReminder")
    public R<UserReminderResponse> getUserReminder() {
        Integer userId = getUserId().intValue();
        UserReminder userReminder = userReminderService.findByUserId(userId);
        UserReminderResponse response = convertToResponse(userReminder);
        return R.ok(response);
    }

    /**
     * 保存或更新用户提醒设置
     *
     * @param request 用户提醒设置请求
     * @return 操作结果
     */
    @PostMapping("/saveOrUpdateUserReminder")
    public R<Void> saveOrUpdateUserReminder(@Valid @RequestBody UserReminderRequest request) {
        Integer userId = getUserId().intValue();
        UserReminder userReminder = convertToEntity(request, userId);

        boolean success = userReminderService.saveOrUpdateUserReminder(userReminder);
        if (success) {
            return R.ok();
        } else {
            return R.fail("保存用户提醒设置失败");
        }
    }

    /**
     * 删除用户提醒设置
     *
     * @return 操作结果
     */
    @DeleteMapping("/deleteUserReminder")
    public R<Void> deleteUserReminder() {
        Integer userId = getUserId().intValue();
        boolean success = userReminderService.removeById(userId);
        if (success) {
            return R.ok();
        } else {
            return R.fail("删除用户提醒设置失败");
        }
    }

    /**
     * 更新看剧基金设置
     *
     * @param settings 看剧基金设置
     * @return 操作结果
     */
    @PostMapping("/updateTheaterFund")
    public R<Void> updateTheaterFund(@Valid @RequestBody TheaterFundSettings settings) {
        Integer userId = getUserId().intValue();
        UserReminder userReminder = userReminderService.findByUserId(userId);

        if (userReminder.getId() == null) {
            // 如果不存在记录，创建新记录
            userReminder.setUserId(userId);
        }

        // 将设置对象转换为JSON字符串
        String settingsJson = userReminderService.convertToJson(settings);
        userReminder.setTheaterFundSettings(settingsJson);

        boolean success = userReminderService.saveOrUpdateUserReminder(userReminder);
        if (success) {
            return R.ok();
        } else {
            return R.fail("更新看剧基金设置失败");
        }
    }

    /**
     * 将实体转换为响应DTO
     *
     * @param userReminder 用户提醒设置实体
     * @return 响应DTO
     */
    private UserReminderResponse convertToResponse(UserReminder userReminder) {
        UserReminderResponse response = new UserReminderResponse();
        BeanUtils.copyProperties(userReminder, response);

        // 解析看剧基金设置JSON
        TheaterFundSettings settings = userReminderService.convertFromJson(userReminder.getTheaterFundSettings());
        response.setTheaterFundSettings(settings);

        return response;
    }

    /**
     * 将请求DTO转换为实体
     *
     * @param request 请求DTO
     * @param userId  用户ID
     * @return 实体
     */
    private UserReminder convertToEntity(UserReminderRequest request, Integer userId) {
        UserReminder userReminder = new UserReminder();
        userReminder.setUserId(userId);
        userReminder.setSalaryDay(request.getSalaryDay());
        userReminder.setShowTime(request.getShowTime());
        userReminder.setTicketSaleTime(request.getTicketSaleTime());

        // 将看剧基金设置转换为JSON字符串
        if (request.getTheaterFundSettings() != null) {
            String settingsJson = userReminderService.convertToJson(request.getTheaterFundSettings());
            userReminder.setTheaterFundSettings(settingsJson);
        }

        return userReminder;
    }
}
