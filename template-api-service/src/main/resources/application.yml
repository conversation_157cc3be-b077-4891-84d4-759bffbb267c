# 环境配置选择
spring:
  profiles:
    active: prod
  thymeleaf:
    suffix: .html
    prefix: classpath:/templates/
    cache: false
    encoding: utf-8

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认一天）
  expireTime: 21600

# MyBatis-plus配置
mybatis-plus:
  # 搜索指定包别名
  type-aliases-package: com.youying.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  config-location: classpath:mybatis/mybatis-config.xml
  # 加载全局的配置文件
  configuration:
    map-underscore-to-camel-case: true
    # SQL打印
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      # 主键类型 0:数据库ID自增
      id-type: AUTO
      # 多租户
      #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
      field-strategy: NOT_NULL
      # 默认数据库表下划线命名
      table-underline: true

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
#rocketmq:
#  name-server: 127.0.0.1:9876
#  producer:
#    group: springBootGroup
# 数据库变更监控配置
database:
  change:
    monitor:
      # 是否启用数据库变更监控
      enabled: true
      # 需要监控的表名列表
      monitored-tables:
        - t_repertoire
        - t_theater
      # 是否启用异步处理
      async-enabled: true
      # 异步处理线程池大小
      async-thread-pool-size: 5
      # 是否记录详细日志
      verbose-logging: false
      # 缓存刷新超时时间（毫秒）
      cache-refresh-timeout: 5000
