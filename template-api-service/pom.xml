<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>friendbetter</artifactId>
        <groupId>com.youying</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>digital-collections-user</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <!-- 阿里数据库连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.youying</groupId>
            <artifactId>template-framework-service</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.14</version>
                <configuration>
                    <mainClass>com.youying.ApplicationMain</mainClass>
                    <!--解决windows命令行窗口中文乱码-->
                    <jvmArguments>-Dfile.encoding=UTF-8</jvmArguments>
                    <!-- 配置为瘦JAR，不包含依赖 -->
                    <layout>ZIP</layout>
                    <includes>
                        <include>
                            <groupId>nothing</groupId>
                            <artifactId>nothing</artifactId>
                        </include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <!-- 跳过repackage，使用原始JAR -->
                            <skip>true</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- 此插件用于将依赖包抽出-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.1</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <!--是否排除传递性-->
                            <excludeTransitive>false</excludeTransitive>
                            <!--是否去掉 jar 包版本信息-->
                            <stripVersion>false</stripVersion>
                            <!--包含范围：包含所有运行时需要的依赖，包括system scope-->
                            <includeScope>compile</includeScope>
                            <!--包含system scope的依赖-->
                            <includeSystemScope>true</includeSystemScope>
                        </configuration>
                    </execution>

                </executions>
            </plugin>
            <!-- 添加jar插件来生成包含正确MANIFEST.MF的JAR -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.youying.ApplicationMain</mainClass>
                        </manifest>
                        <manifestEntries>
                            <!-- 手动添加system scope的依赖到classpath -->
                            <Class-Path>lib/digital-1.0.0.jar</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <!-- 使用antrun插件复制system scope的依赖 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>copy-system-jar</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <tasks>
                                <copy file="../template-server-service/src/main/resources/lib/collection-open-1.0.3-SNAPSHOT.jar"
                                      tofile="${project.build.directory}/lib/digital-1.0.0.jar"/>
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
