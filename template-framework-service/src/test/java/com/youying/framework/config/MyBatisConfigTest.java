package com.youying.framework.config;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * MyBatis配置测试
 */
public class MyBatisConfigTest {

    @Test
    public void testSetTypeAliasesPackage() {
        // 测试类型别名包扫描
        String typeAliasesPackage = "com.youying.**.domain";
        
        try {
            String result = MyBatisConfig.setTypeAliasesPackage(typeAliasesPackage);
            System.out.println("扫描到的包: " + result);
            
            // 验证结果不为空
            assertNotNull(result);
            assertFalse(result.isEmpty());
            
            // 验证包含预期的包名
            assertTrue(result.contains("com.youying.system.domain"));
            
        } catch (Exception e) {
            System.err.println("类型别名包扫描失败: " + e.getMessage());
            e.printStackTrace();
            fail("类型别名包扫描应该成功");
        }
    }
}
