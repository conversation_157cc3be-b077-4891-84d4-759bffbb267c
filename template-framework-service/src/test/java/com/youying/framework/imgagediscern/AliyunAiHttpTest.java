package com.youying.framework.imgagediscern;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云AI HTTP连接独立测试
 * 不依赖Spring容器，直接测试HTTP连接
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class AliyunAiHttpTest {

    private static final String API_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation";
    private static final String API_KEY = "sk-e67668fbbb9a448d895b3782f8d330dc";

    /**
     * 测试基本HTTP连接
     */
    @Test
    public void testBasicHttpConnection() {
        try {
            // 构建简单的测试请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "deepseek-r1");

            // 构建input对象
            Map<String, Object> input = new HashMap<>();
            JSONArray messages = new JSONArray();

            JSONObject userMessage = new JSONObject();
            userMessage.put("role", "user");
            userMessage.put("content", "你是谁？");
            messages.add(userMessage);

            input.put("messages", messages);
            requestBody.put("input", input);

            // 构建parameters对象
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("result_format", "message");
            requestBody.put("parameters", parameters);

            // 发送HTTP请求
            HttpRequest request = HttpRequest.post(API_URL)
                    .header("Authorization", "Bearer " + API_KEY)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(requestBody))
                    .timeout(60000);

            log.info("发送请求到: {}", API_URL);
            log.info("请求体: {}", JSON.toJSONString(requestBody));

            HttpResponse response = request.execute();

            log.info("HTTP状态码: {}", response.getStatus());
            log.info("响应头: {}", response.headers());
            log.info("响应体: {}", response.body());

            if (response.isOk()) {
                log.info("✅ HTTP连接测试成功！");

                // 解析响应
                JSONObject responseObj = JSON.parseObject(response.body());
                if (responseObj.containsKey("output")) {
                    log.info("✅ API响应格式正确！");
                } else if (responseObj.containsKey("error")) {
                    log.warn("⚠️ API返回错误: {}", responseObj.getJSONObject("error"));
                }
            } else {
                log.error("❌ HTTP连接失败，状态码: {}", response.getStatus());
            }

        } catch (Exception e) {
            log.error("❌ HTTP连接测试异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试不同模型的兼容性
     */
    @Test
    public void testModelCompatibility() {
        String[] models = { "deepseek-r1", "qwen-turbo", "qwen-plus" };

        for (String model : models) {
            try {
                log.info("测试模型: {}", model);

                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("model", model);

                Map<String, Object> input = new HashMap<>();
                JSONArray messages = new JSONArray();

                JSONObject userMessage = new JSONObject();
                userMessage.put("role", "user");
                userMessage.put("content", "测试");
                messages.add(userMessage);

                input.put("messages", messages);
                requestBody.put("input", input);

                Map<String, Object> parameters = new HashMap<>();
                parameters.put("result_format", "message");
                requestBody.put("parameters", parameters);

                HttpRequest request = HttpRequest.post(API_URL)
                        .header("Authorization", "Bearer " + API_KEY)
                        .header("Content-Type", "application/json")
                        .body(JSON.toJSONString(requestBody))
                        .timeout(30000);

                HttpResponse response = request.execute();

                if (response.isOk()) {
                    log.info("✅ 模型 {} 连接成功", model);
                } else {
                    log.warn("⚠️ 模型 {} 连接失败，状态码: {}", model, response.getStatus());
                }

            } catch (Exception e) {
                log.error("❌ 模型 {} 测试异常: {}", model, e.getMessage());
            }
        }
    }

    /**
     * 测试API Key有效性
     */
    @Test
    public void testApiKeyValidation() {
        try {
            log.info("测试API Key有效性...");

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "deepseek-r1");

            Map<String, Object> input = new HashMap<>();
            JSONArray messages = new JSONArray();

            JSONObject userMessage = new JSONObject();
            userMessage.put("role", "user");
            userMessage.put("content", "hello");
            messages.add(userMessage);

            input.put("messages", messages);
            requestBody.put("input", input);

            Map<String, Object> parameters = new HashMap<>();
            parameters.put("result_format", "message");
            requestBody.put("parameters", parameters);

            HttpRequest request = HttpRequest.post(API_URL)
                    .header("Authorization", "Bearer " + API_KEY)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(requestBody))
                    .timeout(30000);

            HttpResponse response = request.execute();

            if (response.getStatus() == 401) {
                log.error("❌ API Key无效或已过期");
            } else if (response.getStatus() == 403) {
                log.error("❌ API Key权限不足");
            } else if (response.isOk()) {
                log.info("✅ API Key有效");
            } else {
                log.warn("⚠️ 未知状态码: {}, 响应: {}", response.getStatus(), response.body());
            }

        } catch (Exception e) {
            log.error("❌ API Key验证异常: {}", e.getMessage(), e);
        }
    }
}
