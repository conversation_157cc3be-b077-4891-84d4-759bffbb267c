package com.youying.framework.config;

import java.util.Date;

import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.youying.common.utils.SecurityUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2023/4/9
 */
@Slf4j
@Configuration
public class MybatisPlusConfig implements MetaObjectHandler {

    /**
     * 新增自动注入
     *
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            // 安全地获取用户名，避免在多线程环境下出错
            String username = "系统"; // 默认值
            try {
                username = SecurityUtils.getUsername();
            } catch (Exception e) {
                log.warn("无法获取用户名，使用默认值: {}", e.getMessage());
                username = "系统";
            }

            // 只有当字段存在时才进行填充
            if (metaObject.hasSetter("createBy")) {
                this.strictInsertFill(metaObject, "createBy", String.class, username);
            }

            if (metaObject.hasSetter("createTime")) {
                this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
            }

            updateFill(metaObject);
        } catch (Exception e) {
            log.error("自动填充失败，忽略错误继续执行: {}", e.getMessage());
            // 忽略所有错误，确保不影响主流程
        }
    }

    /**
     * 修改自动注入
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            // 安全地获取用户名，避免在多线程环境下出错
            String username = "系统"; // 默认值
            try {
                username = SecurityUtils.getUsername();
            } catch (Exception e) {
                log.warn("无法获取用户名，使用默认值: {}", e.getMessage());
                username = "系统";
            }

            // 只有当字段存在时才进行填充
            if (metaObject.hasSetter("updateBy")) {
                this.setFieldValByName("updateBy", username, metaObject);
            }

            if (metaObject.hasSetter("updateTime")) {
                this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
            }
        } catch (Exception e) {
            log.error("自动填充失败，忽略错误继续执行: {}", e.getMessage());
            // 忽略所有错误，确保不影响主流程
        }
    }

}
