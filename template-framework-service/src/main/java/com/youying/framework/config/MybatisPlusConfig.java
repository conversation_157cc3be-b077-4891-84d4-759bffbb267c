package com.youying.framework.config;

import java.util.Date;

import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.youying.common.utils.SecurityUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2023/4/9
 */
@Slf4j
@Configuration
public class MybatisPlusConfig implements MetaObjectHandler {

    /**
     * 新增自动注入
     *
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        // 安全地获取用户名，避免在多线程环境下出错
        String username = getUsername();
        this.strictInsertFill(metaObject, "createBy", String.class, username);
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        updateFill(metaObject);
    }

    /**
     * 修改自动注入
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        // 安全地获取用户名，避免在多线程环境下出错
        String username = getUsername();
        this.setFieldValByName("updateBy", username, metaObject);
        this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
    }

    /**
     * 安全地获取用户名，避免在多线程环境下出错
     *
     * @return 用户名，如果获取失败则返回默认值
     */
    private String getUsername() {
        try {
            return SecurityUtils.getUsername();
        } catch (Exception e) {
            log.warn("无法获取用户名，使用默认值: {}", e.getMessage());
            return "系统";
        }
    }
}
