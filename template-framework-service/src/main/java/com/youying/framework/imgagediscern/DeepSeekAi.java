package com.youying.framework.imgagediscern;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.youying.common.core.common.AITextResponse;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.StringUtils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * DeepSeek AI图像识别实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class DeepSeekAi implements DiscernStrategy {

    private static final String API_URL = "https://api.deepseek.com/v1/chat/completions";

    private static final String API_KEY = "sk-dccd6e7d2f0a473f83f4d78ff8b951cb";// deepseek 官方

    private static final String SYSTEM_PROMPT = "你是一个专业的演出票据信息提取助手，请严格按以下规则处理OCR数据：\n" +

            "1. 输入源说明\n" +
            "- OCR接口返回的原始数据\n" +

            "2.核心提取规则\n" +

            "票价字段必须优先采用明确标注的实付金额（如\"半价190元\"），若存在优惠信息则省略\n" +
            "剧目名称需合并完整信息，保留英文副标题和主办方信息\n" +
            "剧场名称取标准名称，若无则取并做简繁转换\n" +
            "演出时间必须合并日期+时间，缺失年份补当前年，时间统一格式化为14:00/19:30等标准格式\n" +
            "座位号必须包含\"区域+排+座\"三级完整信息\n" +

            "3.票价处理特别说明\n" +

            "当出现以下情况时：同时存在\"金额380*1\"和\"半价190元\"→提取为\"190元\"\n" +
            "出现\"套票价\"字样 → 不保留套票价格\n" +
            "多档票价并存 → 选择与座位区域匹配的档位\n" +
            "票价处理范例\n" +
            "✓ 正确案例：\"190 元\"\n" +
            "✓ 正确案例：\"学生票凭证入场\"\n" +
            "× 禁止案例：\"¥19:30\"（明显时间误标）\n" +

            "4. 必含字段清单\n" +
            "- 剧目名称（完整名称含英文副标题）\n" +
            "- 剧目短名称（剧目简称）\n" +
            "- 剧场名称（简体标准化） \n" +
            "- 剧场短名称（简体标准化） \n" +
            "- 演出时间（YYYY年MM月DD日 HH:MM）\n" +
            "- 票价（保留小数+元）\n" +
            "- 座位号（必须包含区域/楼层）\n" +

            "5. 禁止行为\n" +
            "- 忽略宣传语等无关文本\n" +
            "- 不得遗漏座位区域信息\n" +
            "- 不得混合冲突数据\n" +

            "提取以下信息并以JSON格式返回：\n" +
            "{\n" +
            "  \"dateTime\": \"演出日期时间(格式：yyyy-MM-dd HH:mm:ss)\",\n" +
            "  \"date\": \"演出日期(格式：yyyyMMdd)\",\n" +
            "  \"time\": \"演出时间(格式：HHmm)\",\n" +
            "  \"seat\": \"座位号\",\n" +
            "  \"row\": \"排数\",\n" +
            "  \"area\": \"区域\",\n" +
            "  \"price\": \"票价(仅数字，去除货币符号)\",\n" +
            "  \"repertoire\": \"剧目名称\",\n" +
            "  \"shortRepertoireName\": \"剧目短名称\",\n" +
            "  \"theater\": \"剧场名称\",\n" +
            "  \"shortTheaterName\": \"剧场短名称\",\n" +
            "  \"address\": \"完整剧场地址信息,不是座位号\",\n" +
            "  \"textStr\": \"票据上的所有文字内容\"\n" +
            "}\n";

    /**
     * 票面识别方法
     */
    @Override
    public AITextResponse recognition(String ticketText) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = buildRequestBody(ticketText);

            // 调用DeepSeek API
            String responseJson = callDeepSeekAPI(requestBody);

            // 解析响应并构建AITextResponse
            AITextResponse aiTextResponse = parseResponse(responseJson);

            return aiTextResponse;

        } catch (Exception e) {
            log.error("{} --> DeepSeek AI 票面识别异常：{}", DateUtil.now(), e.getMessage(), e);
            throw new ServiceException("DeepSeek AI 票面识别失败: " + e.getMessage());
        }
    }

    @Override
    public AITextResponse imageRecognition(String imageBase64Str) {
        return null;
    }

    @Override
    public AITextResponse imageRecognition(String imageBase64Str, Long scanningId) {
        return null;
    }

    /**
     * 构建请求体
     */
    private Map<String, Object> buildRequestBody(String ticketText) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "deepseek-chat");
        requestBody.put("max_tokens", 2000);
        requestBody.put("temperature", 0.1);

        // 构建消息数组
        JSONArray messages = new JSONArray();

        // 系统提示消息
        JSONObject systemMessage = new JSONObject();
        systemMessage.put("role", "system");
        systemMessage.put("content", SYSTEM_PROMPT);
        messages.add(systemMessage);

        // 用户消息（包含图像）
        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");

        JSONArray content = new JSONArray();

        // 文本内容
        JSONObject textContent = new JSONObject();
        textContent.put("type", "text");
        textContent.put("text", ticketText);
        content.add(textContent);

        userMessage.put("content", content);
        messages.add(userMessage);

        requestBody.put("messages", messages);

        return requestBody;
    }

    /**
     * 调用DeepSeek API
     */
    private String callDeepSeekAPI(Map<String, Object> requestBody) {
        HttpRequest request = HttpRequest.post(API_URL)
                .header("Authorization", "Bearer " + API_KEY)
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(requestBody))
                .timeout(60000); // 60秒超时

        HttpResponse response = request.execute();

        if (!response.isOk()) {
            throw new ServiceException("DeepSeek API调用失败，HTTP状态码: " + response.getStatus() +
                    ", 响应: " + response.body());
        }

        return response.body();
    }

    /**
     * 解析API响应
     */
    private AITextResponse parseResponse(String responseJson) {
        try {
            JSONObject responseObj = JSON.parseObject(responseJson);
            JSONArray choices = responseObj.getJSONArray("choices");

            if (choices == null || choices.isEmpty()) {
                throw new ServiceException("DeepSeek API响应格式错误：choices为空");
            }

            JSONObject firstChoice = choices.getJSONObject(0);
            JSONObject message = firstChoice.getJSONObject("message");
            String content = message.getString("content");

            // 提取JSON内容
            String jsonContent = extractJsonFromContent(content);
            JSONObject extractedData = JSON.parseObject(jsonContent);

            // 构建AITextResponse
            AITextResponse aiTextResponse = new AITextResponse();

            // 设置基本信息
            aiTextResponse.setDateTime(extractedData.getString("dateTime"));
            aiTextResponse.setDate(extractedData.getString("date"));
            aiTextResponse.setTime(extractedData.getString("time"));
            aiTextResponse.setSeat(extractedData.getString("seat"));
            aiTextResponse.setRow(extractedData.getString("row"));
            aiTextResponse.setArea(extractedData.getString("area"));
            aiTextResponse.setPrice(extractedData.getString("price"));
            aiTextResponse.setRepertoire(extractedData.getString("repertoire"));
            aiTextResponse.setTheater(extractedData.getString("theater"));
            aiTextResponse.setAddress(extractedData.getString("address"));
            aiTextResponse.setTextStr(extractedData.getString("textStr"));
            aiTextResponse.setShortRepertoireName(extractedData.getString("shortRepertoireName"));
            aiTextResponse.setShortTheaterName(extractedData.getString("shortTheaterName"));

            // 设置API响应相关信息
            aiTextResponse.setText(content);
            aiTextResponse.setBody(responseJson);
            aiTextResponse.setRequestId(responseObj.getString("id"));

            // 处理时间列表
            processTimeList(aiTextResponse);

            // 后处理
            postProcessAiText(aiTextResponse);

            return aiTextResponse;

        } catch (Exception e) {
            log.error("解析DeepSeek API响应失败: {}", e.getMessage(), e);
            throw new ServiceException("解析DeepSeek API响应失败: " + e.getMessage());
        }
    }

    /**
     * 从内容中提取JSON
     */
    private String extractJsonFromContent(String content) {
        // 查找JSON内容的开始和结束位置
        int startIndex = content.indexOf("{");
        int endIndex = content.lastIndexOf("}");

        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {
            throw new ServiceException("DeepSeek API响应中未找到有效的JSON格式数据");
        }

        return content.substring(startIndex, endIndex + 1);
    }

    /**
     * 处理时间列表
     */
    private void processTimeList(AITextResponse aiTextResponse) {
        ArrayList<String> timeList = new ArrayList<>();

        if (StringUtils.isNotBlank(aiTextResponse.getDate())) {
            timeList.add(aiTextResponse.getDate());
        }

        if (StringUtils.isNotBlank(aiTextResponse.getTime())) {
            timeList.add(aiTextResponse.getTime());
        }

        // 组合日期和时间
        if (StringUtils.isNotBlank(aiTextResponse.getDate()) &&
                StringUtils.isNotBlank(aiTextResponse.getTime())) {
            timeList.add(aiTextResponse.getDate() + aiTextResponse.getTime());
        }

        aiTextResponse.setTimeList(timeList);
    }

    /**
     * 后处理AI文本结果
     */
    private void postProcessAiText(AITextResponse aiText) {
        // 确保座位和排数格式正确
        if (StringUtils.isNotBlank(aiText.getSeat()) &&
                !aiText.getSeat().contains("座") && !aiText.getSeat().contains("号")) {
            aiText.setSeat(aiText.getSeat() + "座");
        }

        if (StringUtils.isNotBlank(aiText.getRow()) && !aiText.getRow().contains("排")) {
            aiText.setRow(aiText.getRow() + "排");
        }

        // 处理价格，确保只包含数字
        if (StringUtils.isNotBlank(aiText.getPrice())) {
            aiText.setPrice(extractDigitsFromPrice(aiText.getPrice()));
        }
    }

    /**
     * 从价格字符串中提取数字
     */
    private String extractDigitsFromPrice(String priceStr) {
        if (StringUtils.isBlank(priceStr)) {
            return "0";
        }

        // 使用正则表达式提取数字
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(priceStr);

        if (matcher.find()) {
            return matcher.group();
        }

        return "0";
    }
}
