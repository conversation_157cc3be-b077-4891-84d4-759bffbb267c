# 阿里云AI HTTP接口使用说明

## 概述

本项目已将阿里云AI接口调整为HTTP方式访问，使用`deepseek-r1`模型，完全兼容阿里云DashScope API。

## 主要修改

### 1. 模型更换

- 原模型：`qwen-turbo`
- 新模型：`deepseek-r1`（与您提供的curl示例保持一致）

### 2. 参数优化

- 移除了`max_tokens`和`temperature`参数（deepseek-r1可能不支持）
- 保留了`result_format: message`参数

### 3. API Key配置

- 支持从环境变量`DASHSCOPE_API_KEY`读取
- 支持从配置文件`aliyun.ai.api.key`读取
- 提供默认值作为后备方案

## 配置方式

### 方式1：环境变量（推荐）

```bash
export DASHSCOPE_API_KEY="your-api-key-here"
```

### 方式2：application.yml配置文件

```yaml
aliyun:
  ai:
    api:
      key: your-api-key-here
```

### 方式3：启动参数

```bash
java -jar app.jar --aliyun.ai.api.key=your-api-key-here
```

## HTTP请求格式

代码生成的HTTP请求格式与您提供的curl示例完全一致：

```json
{
    "model": "deepseek-r1",
    "input": {
        "messages": [
            {
                "role": "system",
                "content": "系统提示词..."
            },
            {
                "role": "user", 
                "content": "用户输入内容"
            }
        ]
    },
    "parameters": {
        "result_format": "message"
    }
}
```

## 测试验证

### 方法1: 运行独立测试程序（推荐）

```bash
# 在项目根目录执行
cd template-framework-service
mvn exec:java -Dexec.mainClass="com.youying.framework.imgagediscern.AliyunAiTestRunner"
```

### 方法2: 运行JUnit测试

```bash
# 在项目根目录执行
mvn test -Dtest=AliyunAiHttpTest -pl template-framework-service
```

### 方法3: 使用提供的脚本

```bash
# 在项目根目录执行
./run-tests.sh
```

### 方法4: 在IDE中直接运行

- 运行 `AliyunAiTestRunner.main()` 方法
- 或运行 `AliyunAiHttpTest` 测试类

## 使用示例

### 票据识别

```java
String ticketText = "演出名称：音乐剧《猫》\n演出时间：2024年12月25日 19:30\n...";
AITextResponse response = aliyunAi.recognition(ticketText);
```

## 错误处理

- HTTP状态码非200时会抛出`ServiceException`
- API响应格式错误时会抛出`ServiceException`
- 网络超时设置为60秒

## 注意事项

1. 确保API Key有效且有足够的调用额度
2. deepseek-r1模型的响应格式可能与qwen-turbo略有不同
3. 现有的SYSTEM_PROMPT保持不变，确保业务逻辑一致性
4. 建议在生产环境中使用环境变量配置API Key，避免硬编码

## 依赖项

- HuTool HTTP客户端
- FastJSON2 JSON处理
- Spring Boot配置管理
