package com.youying.system.service.impl;

import java.util.Date;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youying.common.core.domain.entity.UserReminder;
import com.youying.system.domain.userreminder.TheaterFundSettings;
import com.youying.system.mapper.UserReminderMapper;
import com.youying.system.service.UserReminderService;

/**
 * <p>
 * 用户提醒设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Service
public class UserReminderServiceImpl extends ServiceImpl<UserReminderMapper, UserReminder>
        implements UserReminderService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 根据用户ID查询用户提醒设置
     *
     * @param userId 用户ID
     * @return 用户提醒设置
     */
    @Override
    public UserReminder findByUserId(Integer userId) {
        UserReminder userReminder = baseMapper.findByUserId(userId);
        if (userReminder == null) {
            // 如果用户没有设置，返回空对象
            userReminder = new UserReminder();
            userReminder.setUserId(userId);
        }
        return userReminder;
    }

    /**
     * 保存或更新用户提醒设置
     *
     * @param userReminder 用户提醒设置
     * @return 是否成功
     */
    @Override
    public boolean saveOrUpdateUserReminder(UserReminder userReminder) {
        if (userReminder.getUserId() == null) {
            return false;
        }

        // 查询是否已存在
        UserReminder existingReminder = baseMapper.findByUserId(userReminder.getUserId());

        if (existingReminder != null) {
            // 更新现有记录
            userReminder.setId(existingReminder.getId());
            return updateById(userReminder);
        } else {
            // 创建新记录
            userReminder.setCreateTime(new Date());
            return save(userReminder);
        }
    }

    /**
     * 将TheaterFundSettings对象转换为JSON字符串
     *
     * @param settings 看剧基金设置对象
     * @return JSON字符串
     */
    public String convertToJson(TheaterFundSettings settings) {
        if (settings == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(settings);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("转换看剧基金设置为JSON失败", e);
        }
    }

    /**
     * 将JSON字符串转换为TheaterFundSettings对象
     *
     * @param json JSON字符串
     * @return 看剧基金设置对象
     */
    public TheaterFundSettings convertFromJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, TheaterFundSettings.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("解析看剧基金设置JSON失败", e);
        }
    }
}
