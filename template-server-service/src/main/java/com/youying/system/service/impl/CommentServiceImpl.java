package com.youying.system.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Comment;
import com.youying.common.core.domain.entity.Kudos;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.core.domain.entity.User;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.enums.Enums.UserInteractionFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.comment.CommentRequest;
import com.youying.system.domain.comment.CommentResponse;
import com.youying.system.domain.comment.SaveCommentRequest;
import com.youying.system.mapper.CommentMapper;
import com.youying.system.service.CommentService;
import com.youying.system.service.KudosService;
import com.youying.system.service.RepertoireService;
import com.youying.system.service.UserInteractionService;
import com.youying.system.service.UserReceivingRecordsService;
import com.youying.system.service.UserService;
import com.youying.system.service.UserTicketGroupService;

/**
 * <p>
 * 剧目剧场评论 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {
    @Autowired
    private RepertoireService repertoireService;
    @Autowired
    private UserInteractionService userInteractionService;
    @Autowired
    private KudosService kudosService;
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserTicketGroupService userTicketGroupService;

    /**
     * 剧目剧场评论
     *
     * @param request
     * @return
     */
    @Override
    public List<CommentResponse> listByPage(CommentRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧场、剧目对应评论（填加评论时的两段评论）
     *
     * @param id
     * @return
     */
    @Override
    public CommentResponse findMappingComment(Long id) {
        return baseMapper.findMappingComment(id);
    }

    /**
     * 添加评论
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Long add(SaveCommentRequest request) {
        Date nowTime = new Date();
        Comment comment = request.getComment();

        comment.setRepertoireInfoDetailId(request.getRepertoireInfoDetailId());
        comment.setUserId(SecurityUtils.getUserId());
        comment.setParentId(comment.getParentId() == null ? 0L : comment.getParentId());
        comment.setCommentTime(nowTime);
        comment.setTop(StatusFlag.PROHIBITION.getCode());
        comment.setIpAddress(SecurityUtils.getLoginLocation());
        comment.setStatus(StatusFlag.OK.getCode());

        // 判断是否为父集评论
        if (comment.getParentId() == null || comment.getParentId() == 0L) {
            // 评分-好评率
            if (comment.getGrade() > 0) {
                Repertoire repertoire = repertoireService.getById(request.getRepertoireId());
                Double sumGrade = baseMapper.findRepertoireSumGrade(repertoire.getId()) + comment.getGrade();
                Double sumPeople = baseMapper.findRepertoireSumUser(repertoire.getId()) + 1;
                if (sumPeople > 0) {
                    Double score = sumGrade / sumPeople;
                    repertoire.setRating(score);
                    repertoire.setGoodRatingRate(score);
                    repertoireService.updateById(repertoire);
                }
            }
            // 修改用户观影记录评论
            UserReceivingRecords userReceivingRecords = userReceivingRecordsService
                    .getById(request.getUserReceivingRecordsId());
            save(comment);
            userReceivingRecords.setCommentId(comment.getId());
            userReceivingRecordsService.updateById(userReceivingRecords);
        } else {
            Comment parentComment = getById(comment.getParentId());
            comment.setTheaterId(parentComment.getTheaterId());
            comment.setRepertoireInfoDetailId(parentComment.getRepertoireInfoDetailId());
            comment.setRepertoireId(parentComment.getRepertoireId());
            save(comment);

            // 添加回复记录互动
            userInteractionService.add(parentComment.getId(), comment.getId(), parentComment.getUserId(),
                    comment.getRepertoireId(), UserInteractionFlag.COMMENT.getCode());
        }

        if (request.getTheaterFlag() != null) {
            Kudos kudos = new Kudos();
            kudos.setCommentId(comment.getId());
            kudos.setTheaterId(comment.getTheaterId());
            kudos.setType(request.getTheaterFlag());
            kudos.setUserId(SecurityUtils.getUserId());
            kudosService.save(kudos);
        }
        if (request.getRepertoireFlag() != null) {
            Kudos kudos = new Kudos();
            kudos.setCommentId(comment.getId());
            kudos.setRepertoireId(request.getRepertoireId());
            kudos.setType(request.getRepertoireFlag());
            kudos.setUserId(SecurityUtils.getUserId());
            kudosService.save(kudos);

            // 添加电子票分组
            userTicketGroupService.add(request.getRepertoireFlag(), request.getUserReceivingRecordsId(),
                    request.getRepertoireId());
        }

        return comment.getId();
    }

    /**
     * 查询用户评论次数
     *
     * @param repertoireId
     * @param theaterId
     * @param repertoireInfoDetailId
     * @return
     */
    @Override
    public Long findUserAdd(Long repertoireId, Long theaterId, Long repertoireInfoDetailId) {
        return baseMapper.findUserAdd(repertoireId, theaterId, repertoireInfoDetailId);
    }

    /**
     * 查询评论详情
     *
     * @param id
     * @param userId
     * @return
     */
    @Override
    public CommentResponse details(Long id, Long userId) {
        return baseMapper.details(id, userId);
    }

    /**
     * 回复
     *
     * @param comment
     * @return
     */
    @Override
    @Transactional
    public Long reply(Comment comment) {
        // 判断用户是否被禁言
        User user = userService.getById(SecurityUtils.getUserId());
        if (user == null || !StatusFlag.OK.getCode().equals(user.getSpeakStatus())) {
            throw new SecurityException("您已被禁言，无法回复");
        }

        Comment parentComment = getById(comment.getParentId());
        comment.setUserId(SecurityUtils.getUserId());
        comment.setReplyId(comment.getReplyId());

        comment.setTheaterId(parentComment.getTheaterId());
        comment.setParentId(parentComment.getId());
        comment.setRepertoireId(parentComment.getRepertoireId());
        comment.setRepertoireInfoDetailId(parentComment.getRepertoireInfoDetailId());
        comment.setCommentTime(new Date());
        comment.setGrade(0);
        comment.setTop(StatusFlag.PROHIBITION.getCode());
        comment.setIpAddress(SecurityUtils.getLoginUser().getIpaddr());
        save(comment);

        userInteractionService.add(comment.getParentId(), comment.getId(), parentComment.getUserId(),
                comment.getRepertoireId(), UserInteractionFlag.COMMENT.getCode());

        return comment.getId();
    }

    /**
     * 查询用户评论列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<CommentResponse> listByUser(CommentRequest request) {
        return baseMapper.listByUser(request);
    }

    /**
     * 查询用户回复列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<CommentResponse> listReplyByUser(CommentRequest request) {
        return baseMapper.listReplyByUser(request);
    }

    /**
     * 查询用户评论条数
     *
     * @param request
     * @return
     */
    @Override
    public Long listByUserCount(CommentRequest request) {
        return baseMapper.listByUserCount(request);
    }

    /**
     * 获取评论数据分页列表
     * 每条评论数据显示评论ID、评论发布人头像、名称、剧目名称、场次名称、剧场名称、
     * 剧目图片或者上传的票的图片、评论内容、评论发布时间、评论被点赞的次数、
     * 评论被转发的次数、评论被关注的次数
     *
     * @param request
     * @return
     */
    @Override
    public List<CommentResponse> getCommentPageList(CommentRequest request) {
        return baseMapper.getCommentPageList(request);
    }

    /**
     * 评论详情接口
     * 包含评论的评论ID、评论发布人头像、名称、评论发布的地点、ip、剧目名称、场次名称、剧场名称、
     * 剧目图片或者上传的票的图片、评论内容、评论发布时间、评论被点赞的次数、评论被转发的次数、
     * 评论被关注的次数、评论的回复列表（最多两级）
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return
     */
    @Override
    public CommentResponse getCommentDetail(Long commentId, Long userId) {
        return baseMapper.getCommentDetail(commentId, userId);
    }

}
