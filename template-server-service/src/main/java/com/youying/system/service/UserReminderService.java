package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserReminder;
import com.youying.system.domain.userreminder.TheaterFundSettings;

/**
 * <p>
 * 用户提醒设置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface UserReminderService extends IService<UserReminder> {

    /**
     * 根据用户ID查询用户提醒设置
     *
     * @param userId 用户ID
     * @return 用户提醒设置
     */
    UserReminder findByUserId(Integer userId);

    /**
     * 保存或更新用户提醒设置
     *
     * @param userReminder 用户提醒设置
     * @return 是否成功
     */
    boolean saveOrUpdateUserReminder(UserReminder userReminder);

    /**
     * 将TheaterFundSettings对象转换为JSON字符串
     *
     * @param settings 看剧基金设置对象
     * @return JSON字符串
     */
    String convertToJson(TheaterFundSettings settings);

    /**
     * 将JSON字符串转换为TheaterFundSettings对象
     *
     * @param json JSON字符串
     * @return 看剧基金设置对象
     */
    TheaterFundSettings convertFromJson(String json);
}
