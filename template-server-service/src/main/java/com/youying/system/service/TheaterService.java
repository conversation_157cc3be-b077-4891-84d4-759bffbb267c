package com.youying.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Theater;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;

/**
 * <p>
 * 剧场表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface TheaterService extends IService<Theater> {

    /**
     * 查询首页剧场
     *
     * @param userId
     * @return
     */
    List<TheaterResponse> listByIndex(Long userId);

    /**
     * 剧场列表
     *
     * @param request
     * @return
     */
    List<TheaterResponse> listByPage(TheaterRequest request);

    /**
     * 查询剧场详情
     *
     * @param id
     * @param userId
     * @return
     */
    TheaterResponse details(Long id, Long userId);

    /**
     * 模糊查询是否存在该剧场
     *
     * @param str
     * @return
     */
    List<Theater> findTheaterShortName(String str);

    /**
     * 查询剧场橱窗列表
     *
     * @param theaterId
     * @return
     */
    List<RepertoireDisplayResponse> findTheaterDisplay(Long theaterId);

    /**
     * 通过相似度算法查找最匹配的剧场
     * 
     * @param theaterName 剧场名称
     * @return 最匹配的剧场
     */
    Theater findBestMatchTheater(String theaterName);
}
