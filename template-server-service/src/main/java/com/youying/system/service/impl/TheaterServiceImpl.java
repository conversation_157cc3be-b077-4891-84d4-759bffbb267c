package com.youying.system.service.impl;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Theater;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;
import com.youying.system.mapper.TheaterMapper;
import com.youying.system.service.TheaterService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 剧场表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
@Slf4j
public class TheaterServiceImpl extends ServiceImpl<TheaterMapper, Theater> implements TheaterService {

    /**
     * 查询首页剧场
     *
     * @param userId
     * @return
     */
    @Override
    public List<TheaterResponse> listByIndex(Long userId) {
        return baseMapper.listByIndex(userId);
    }

    /**
     * 剧场列表
     *
     * @param request
     * @return
     */
    @Override
    public List<TheaterResponse> listByPage(TheaterRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧场详情
     *
     * @param id
     * @param userId
     * @return
     */
    @Override
    public TheaterResponse details(Long id, Long userId) {
        return baseMapper.details(id, userId);
    }

    /**
     * 模糊查询是否存在该剧场
     *
     * @param str
     * @return
     */
    @Override
    public List<Theater> findTheaterShortName(String str) {
        return baseMapper.findTheaterShortName(str);
    }

    /**
     * 查询剧场橱窗列表
     *
     * @param theaterId
     * @return
     */
    @Override
    public List<RepertoireDisplayResponse> findTheaterDisplay(Long theaterId) {
        return baseMapper.findTheaterDisplay(theaterId == null ? 0 : theaterId);
    }

    /**
     * 通过相似度算法查找最匹配的剧场
     * 
     * @param theaterName 剧场名称
     * @return 最匹配的剧场
     */
    @Override
    public Theater findBestMatchTheater(String theaterName) {
        if (StringUtils.isBlank(theaterName)) {
            return null;
        }

        // 获取所有剧场
        List<Theater> allTheaters = findTheaterShortName(theaterName);

        if (CollectionUtils.isEmpty(allTheaters)) {
            return null;
        }

        Theater bestMatch = null;
        double highestScore = 0.0;

        for (Theater theater : allTheaters) {
            double matchDegree = com.youying.common.utils.StringUtils.matchDegree(theaterName, theater.getName());
            if (matchDegree > highestScore && matchDegree >= 0.6) { // 设置最低匹配度阈值
                highestScore = matchDegree;
                bestMatch = theater;
            }
        }

        log.info("剧场匹配结果: 输入={}, 最佳匹配={}, 相似度={}",
                theaterName, bestMatch != null ? bestMatch.getName() : "无", highestScore);

        return bestMatch;
    }
}
