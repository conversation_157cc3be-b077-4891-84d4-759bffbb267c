package com.youying.system.service.impl;

import java.util.Date;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.ImageScanRecord;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.ip.IpUtils;
import com.youying.system.mapper.ImageScanRecordMapper;
import com.youying.system.service.ImageScanRecordService;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Service
public class ImageScanRecordServiceImpl extends ServiceImpl<ImageScanRecordMapper, ImageScanRecord>
        implements ImageScanRecordService {

    /**
     * 添加
     *
     * @param imageScanRecord
     */
    @Override
    @Transactional
    public void add(ImageScanRecord imageScanRecord) {
        imageScanRecord.setUserId(SecurityUtils.getUserId());
        imageScanRecord.setIpAddress(IpUtils.getIpAddr());
        save(imageScanRecord);
    }

    /**
     * 查询扫描成功次数
     *
     * @param portfolioId
     * @return
     */
    @Override
    public Long findPortfolioCount(Long portfolioId) {
        return count(new LambdaQueryWrapper<ImageScanRecord>()
                .eq(ImageScanRecord::getPortfolioId, portfolioId));
    }

    /**
     * 异步保存扫描记录（避免安全上下文问题）
     *
     * @param imageScanRecord
     */
    @Override
    public void saveAsync(ImageScanRecord imageScanRecord) {
        // 确保创建时间已设置
        if (imageScanRecord.getCreateTime() == null) {
            imageScanRecord.setCreateTime(new Date());
        }

        // 使用原生 SQL 插入，完全避免自动填充机制
        baseMapper.insertAsync(imageScanRecord);
    }
}
