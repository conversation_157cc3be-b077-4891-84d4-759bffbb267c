package com.youying.system.domain.userreminder;

import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 用户提醒设置请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
public class UserReminderRequest {

    /**
     * 发薪日(1-31)
     */
    @Min(value = 1, message = "发薪日必须在1-31之间")
    @Max(value = 31, message = "发薪日必须在1-31之间")
    private Integer salaryDay;

    /**
     * 开演时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showTime;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ticketSaleTime;

    /**
     * 看剧基金设置
     */
    @Valid
    private TheaterFundSettings theaterFundSettings;
}
