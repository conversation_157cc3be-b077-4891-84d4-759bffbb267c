package com.youying.system.domain.userreminder;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * 看剧基金设置DTO
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
public class TheaterFundSettings {

    /**
     * 月工资
     */
    @NotNull(message = "月工资不能为空")
    @DecimalMin(value = "0.01", message = "月工资必须大于0")
    private BigDecimal monthlySalary;

    /**
     * 每天上班时间 (HH:mm格式)
     */
    @NotBlank(message = "上班时间不能为空")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "上班时间格式不正确，应为HH:mm")
    private String workStartTime;

    /**
     * 每天下班时间 (HH:mm格式)
     */
    @NotBlank(message = "下班时间不能为空")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "下班时间格式不正确，应为HH:mm")
    private String workEndTime;

    /**
     * 是否包含周六周日
     */
    @NotNull(message = "是否包含周末不能为空")
    private Boolean includeWeekends;
}
