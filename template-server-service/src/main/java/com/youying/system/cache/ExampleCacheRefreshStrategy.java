package com.youying.system.cache;

import com.youying.common.cache.CacheRefreshStrategy;
import com.youying.common.core.redis.RedisCache;
import com.youying.common.event.DatabaseChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

/**
 * 示例缓存刷新策略
 * 展示如何为新表实现缓存刷新策略
 * 
 * 注意：这是一个示例类，实际使用时请根据具体表的需求进行实现
 * 如果不需要此示例，可以删除此文件
 *
 * <AUTHOR>
 */
@Slf4j
// @Component  // 取消注释以启用此策略
public class ExampleCacheRefreshStrategy implements CacheRefreshStrategy {

    @Autowired
    private RedisCache redisCache;

    @Override
    public String getSupportedTableName() {
        // 返回支持的表名，例如：t_example
        return "t_example";
    }

    @Override
    public void refreshCache(DatabaseChangeEvent event) {
        log.info("Refreshing example cache for operation: {}, primaryKey: {}",
                event.getOperationType(), event.getPrimaryKey());

        try {
            // 清理相关缓存
            clearExampleCache(event);

            log.info("Successfully refreshed example cache for primaryKey: {}", event.getPrimaryKey());
        } catch (Exception e) {
            log.error("Error refreshing example cache for primaryKey {}: {}",
                    event.getPrimaryKey(), e.getMessage(), e);
        }
    }

    /**
     * 清理示例表相关缓存
     */
    private void clearExampleCache(DatabaseChangeEvent event) {
        Object primaryKey = event.getPrimaryKey();

        // 1. 清理详情缓存
        if (primaryKey != null) {
            String detailCacheKey = "example:info:" + primaryKey;
            redisCache.deleteObject(detailCacheKey);
            log.debug("Cleared example detail cache: {}", detailCacheKey);
        }

        // 2. 清理列表相关缓存
        clearCacheByPattern("example:list:*");

        // 3. 清理通用缓存
        clearCacheByPattern("example:*");

        // 4. 根据操作类型执行特定的缓存清理
        switch (event.getOperationType()) {
            case INSERT:
                handleInsertCache(event);
                break;
            case UPDATE:
                handleUpdateCache(event);
                break;
            case DELETE:
                handleDeleteCache(event);
                break;
        }
    }

    /**
     * 处理INSERT操作的缓存清理
     */
    private void handleInsertCache(DatabaseChangeEvent event) {
        log.debug("Handling INSERT cache refresh for example: {}", event.getPrimaryKey());
        
        // 新增记录时，需要清理列表缓存
        redisCache.deleteObject("example:total:count");
        clearCacheByPattern("example:page:*");
    }

    /**
     * 处理UPDATE操作的缓存清理
     */
    private void handleUpdateCache(DatabaseChangeEvent event) {
        log.debug("Handling UPDATE cache refresh for example: {}", event.getPrimaryKey());
        
        Object primaryKey = event.getPrimaryKey();
        if (primaryKey != null) {
            // 清理详情缓存
            String detailKey = "example:info:" + primaryKey;
            redisCache.deleteObject(detailKey);
            
            // 清理相关统计缓存
            redisCache.deleteObject("example:stats:" + primaryKey);
        }
        
        // 清理可能受影响的列表缓存
        clearCacheByPattern("example:search:*");
    }

    /**
     * 处理DELETE操作的缓存清理
     */
    private void handleDeleteCache(DatabaseChangeEvent event) {
        log.debug("Handling DELETE cache refresh for example: {}", event.getPrimaryKey());
        
        Object primaryKey = event.getPrimaryKey();
        if (primaryKey != null) {
            // 清理所有相关缓存
            clearCacheByPattern("example:*:" + primaryKey);
            clearCacheByPattern("example:*:" + primaryKey + ":*");
        }
        
        // 清理列表和统计缓存
        clearCacheByPattern("example:list:*");
        clearCacheByPattern("example:count:*");
        redisCache.deleteObject("example:total:count");
    }

    /**
     * 根据模式清理缓存
     */
    private void clearCacheByPattern(String pattern) {
        try {
            Set<String> keys = redisCache.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisCache.deleteObject(keys);
                log.debug("Cleared {} cache keys matching pattern: {}", keys.size(), pattern);
            }
        } catch (Exception e) {
            log.warn("Failed to clear cache by pattern {}: {}", pattern, e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        return 100; // 普通优先级
    }

    @Override
    public boolean isAsyncSupported() {
        return true; // 支持异步执行
    }
}
