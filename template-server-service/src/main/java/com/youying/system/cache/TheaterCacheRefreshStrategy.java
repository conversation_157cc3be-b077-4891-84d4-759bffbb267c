package com.youying.system.cache;

import com.youying.common.cache.CacheRefreshStrategy;
import com.youying.common.constant.CacheConstants;
import com.youying.common.core.redis.RedisCache;
import com.youying.common.event.DatabaseChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 剧场表缓存刷新策略
 * 当t_theater表发生变更时，清理相关的Redis缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TheaterCacheRefreshStrategy implements CacheRefreshStrategy {

    @Autowired
    private RedisCache redisCache;

    @Override
    public String getSupportedTableName() {
        return "t_theater";
    }

    @Override
    public void refreshCache(DatabaseChangeEvent event) {
        log.info("Refreshing theater cache for operation: {}, primaryKey: {}",
                event.getOperationType(), event.getPrimaryKey());

        try {
            // 清理剧场相关的所有缓存
            clearTheaterCache(event);

            log.info("Successfully refreshed theater cache for primaryKey: {}", event.getPrimaryKey());
        } catch (Exception e) {
            log.error("Error refreshing theater cache for primaryKey {}: {}",
                    event.getPrimaryKey(), e.getMessage(), e);
        }
    }

    /**
     * 清理剧场相关缓存
     */
    private void clearTheaterCache(DatabaseChangeEvent event) {
        Object primaryKey = event.getPrimaryKey();

        // 1. 清理剧场详情缓存
        if (primaryKey != null) {
            String detailCacheKey = CacheConstants.THEATER_INFO_CACHE_KEY + primaryKey;
            redisCache.deleteObject(detailCacheKey);
            log.debug("Cleared theater detail cache: {}", detailCacheKey);
        }

        // 2. 清理剧场列表相关缓存（使用模糊匹配）
        clearCacheByPattern(CacheConstants.THEATER_LIST_CACHE_KEY + "*");

        // 3. 清理剧场通用缓存
        clearCacheByPattern(CacheConstants.THEATER_CACHE_KEY + "*");

        // 4. 根据操作类型执行特定的缓存清理
        switch (event.getOperationType()) {
            case INSERT:
                handleInsertCache(event);
                break;
            case UPDATE:
                handleUpdateCache(event);
                break;
            case DELETE:
                handleDeleteCache(event);
                break;
        }
    }

    /**
     * 处理INSERT操作的缓存清理
     */
    private void handleInsertCache(DatabaseChangeEvent event) {
        // 新增剧场时，需要清理列表缓存，因为列表内容发生了变化
        log.debug("Handling INSERT cache refresh for theater: {}", event.getPrimaryKey());
        
        // 清理推荐剧场缓存
        redisCache.deleteObject("theater:recommend:list");
        
        // 清理首页剧场缓存
        redisCache.deleteObject("theater:home:list");
        
        // 清理地区剧场缓存
        clearCacheByPattern("theater:area:*");
        
        // 清理商家剧场缓存
        clearCacheByPattern("theater:merchant:*");
    }

    /**
     * 处理UPDATE操作的缓存清理
     */
    private void handleUpdateCache(DatabaseChangeEvent event) {
        // 更新剧场时，需要清理详情缓存和相关列表缓存
        log.debug("Handling UPDATE cache refresh for theater: {}", event.getPrimaryKey());
        
        Object primaryKey = event.getPrimaryKey();
        if (primaryKey != null) {
            // 清理剧场详情缓存
            String detailKey = CacheConstants.THEATER_INFO_CACHE_KEY + primaryKey;
            redisCache.deleteObject(detailKey);
            
            // 清理剧场相关的统计缓存
            redisCache.deleteObject("theater:stats:" + primaryKey);
            
            // 清理关注数相关缓存
            redisCache.deleteObject("theater:focus:" + primaryKey);
            
            // 清理剧场座位信息缓存
            redisCache.deleteObject("theater:seats:" + primaryKey);
        }
        
        // 清理可能受影响的列表缓存
        clearCacheByPattern("theater:search:*");
        clearCacheByPattern("theater:location:*");
    }

    /**
     * 处理DELETE操作的缓存清理
     */
    private void handleDeleteCache(DatabaseChangeEvent event) {
        // 删除剧场时，需要清理所有相关缓存
        log.debug("Handling DELETE cache refresh for theater: {}", event.getPrimaryKey());
        
        Object primaryKey = event.getPrimaryKey();
        if (primaryKey != null) {
            // 清理剧场详情缓存
            String detailKey = CacheConstants.THEATER_INFO_CACHE_KEY + primaryKey;
            redisCache.deleteObject(detailKey);
            
            // 清理剧场相关的所有缓存
            clearCacheByPattern("theater:*:" + primaryKey);
            clearCacheByPattern("theater:*:" + primaryKey + ":*");
            
            // 清理剧目-剧场关联缓存
            clearCacheByPattern("repertoire:theater:" + primaryKey + ":*");
        }
        
        // 清理列表缓存
        clearCacheByPattern("theater:list:*");
        clearCacheByPattern("theater:count:*");
        
        // 清理相关的剧目缓存（因为剧目可能关联了这个剧场）
        clearCacheByPattern("repertoire:list:*");
    }

    /**
     * 根据模式清理缓存
     */
    private void clearCacheByPattern(String pattern) {
        try {
            Set<String> keys = redisCache.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisCache.deleteObject(keys);
                log.debug("Cleared {} cache keys matching pattern: {}", keys.size(), pattern);
            }
        } catch (Exception e) {
            log.warn("Failed to clear cache by pattern {}: {}", pattern, e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public boolean isAsyncSupported() {
        return true; // 支持异步执行
    }
}
