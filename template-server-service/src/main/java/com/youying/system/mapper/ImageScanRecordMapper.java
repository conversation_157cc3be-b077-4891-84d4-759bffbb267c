package com.youying.system.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.ImageScanRecord;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface ImageScanRecordMapper extends BaseMapper<ImageScanRecord> {

    /**
     * 异步插入扫描记录（避免自动填充机制）
     *
     * @param record
     * @return
     */
    @Insert("INSERT INTO t_image_scan_record (user_id, device_model, portfolio_id, file_url, ip_address, " +
            "body, word_list, body1, word_list1, request_id, request_id1, final_result, create_time) " +
            "VALUES (#{record.userId}, #{record.deviceModel}, #{record.portfolioId}, #{record.fileUrl}, " +
            "#{record.ipAddress}, #{record.body}, #{record.wordList}, #{record.body1}, #{record.wordList1}, " +
            "#{record.requestId}, #{record.requestId1}, #{record.finalResult}, #{record.createTime})")
    int insertAsync(@Param("record") ImageScanRecord record);

}
