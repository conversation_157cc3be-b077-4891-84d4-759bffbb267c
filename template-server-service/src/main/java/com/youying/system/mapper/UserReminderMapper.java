package com.youying.system.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserReminder;

/**
 * <p>
 * 用户提醒设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface UserReminderMapper extends BaseMapper<UserReminder> {

    /**
     * 根据用户ID查询用户提醒设置
     *
     * @param userId 用户ID
     * @return 用户提醒设置
     */
    UserReminder findByUserId(@Param("userId") Integer userId);
}
