<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserReminderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserReminder">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="salary_day" property="salaryDay"/>
        <result column="show_time" property="showTime"/>
        <result column="ticket_sale_time" property="ticketSaleTime"/>
        <result column="theater_fund_settings" property="theaterFundSettings"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, salary_day, show_time, ticket_sale_time,
        theater_fund_settings, create_time
    </sql>

    <!-- 根据用户ID查询用户提醒设置 -->
    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_user_reminder
        WHERE user_id = #{userId}
        LIMIT 1
    </select>

</mapper>
