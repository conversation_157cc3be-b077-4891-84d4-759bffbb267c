<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserMessageMapper">
    <resultMap id="userMessageMap" type="UserMessageResponse">
        <id column="id" property="id"/>
        <collection property="userMessageInfoList" select="messageInfoQuery" column="id" ofType="UserMessageInfoResponse" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserMessage">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="user_id" property="userId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, theater_id, repertoire_id, user_id, create_by, create_time
    </sql>

    <select id="messageInfoQuery" resultType="UserMessageInfoResponse">
        SELECT
            umi.id,
            umi.body,
            umi.user_id,
            umi.user_merchant_id,
            umi.create_time
        FROM
            t_user_message_info AS umi
        WHERE
            umi.user_message_id = #{id}
        ORDER BY
            create_time
    </select>

    <select id="listByPage" resultType="UserMessageResponse">
        SELECT
            um.id,
            um.repertoire_id,
            um.theater_id,
            um.look_flag,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            r.`cover_picture` AS repertoireCoverPicture,
            t.`cover_picture` AS theaterCoverPicture,
            um.update_time,
            ( SELECT COUNT(1) FROM t_user_message_info WHERE user_message_id = um.id AND user_id = um.user_id AND user_merchant_id > 0 AND look_flag = 0) AS notLookCount,
            ( SELECT body FROM t_user_message_info WHERE user_message_id = um.id ORDER BY create_time DESC LIMIT 1 ) AS lastBody
        FROM
            t_user_message AS um
            LEFT JOIN t_repertoire AS r ON r.id = um.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = um.theater_id
        WHERE
            um.user_id = #{userId}
        ORDER BY
            um.update_time DESC, um.id
    </select>

    <select id="details" resultMap="userMessageMap">
        SELECT
            um.id,
            um.repertoire_id,
            um.theater_id,
            um.look_flag,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            r.`cover_picture` AS repertoireCoverPicture,
            t.`cover_picture` AS theaterCoverPicture
        FROM
            t_user_message AS um
            LEFT JOIN t_repertoire AS r ON r.id = um.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = um.theater_id
            LEFT JOIN t_user AS u ON u.id = um.user_id
        WHERE
            um.id = #{id}
            AND um.user_id = #{userId}
    </select>

</mapper>
